# SMART-5 智能协作规则

> **核心理念**：中文优先 × MCP驱动 × 矛盾转化 × 持续优化

## 📊 核心矛盾分析
**矛盾本质**：用户期望的MCP工具智能分层激活 vs AI传统工具使用习惯

### 🔸 推动力量
- 用户明确要求优先使用MCP工具。
- SMART-5规则的智能分层架构。
- 协作效率提升的需求。

### 🔹 阻力因素
- AI缺乏MCP优先意识。
- 工具选择决策机制不完善。
- 缺乏用户反馈驱动的优化循环。

## 🚀 核心原则
1. **中文优先**：所有交互以简洁中文呈现，优化用户体验。
2. **MCP驱动**：优先激活MCP工具，确保高效协作。
3. **矛盾转化**：将工具选择矛盾转化为协作效率提升机会。
4. **持续优化**：通过用户反馈动态调整工具调用策略。

## 🛠️ 工具分层架构
精简为两层架构，强化MCP工具优先级。

### L0 基础设施 (100%激活)
- **mcp-feedback-enhanced**: 实时反馈收集、系统监控 (`interactive_feedback`)。
- **mcp-server-time**: 时间管理，固定Asia/Shanghai时区。
- **codelf**: 项目信息管理 (`get-project-info`, `update-project-info`).

### L1 核心智能 (按需激活，90%概率)
- **promptx**: 专业角色定制，自动匹配任务需求。
- **knowledge-graph-memory**: 协作模式记忆库 (`create_entities`, `read_graph`, `search_nodes`)。
- **Context 7**: 技术文档支持，优先激活 (`resolve-library-id`, `get-library-docs`)。
- **tavily**: 实时AI搜索 (`tavily-search`)。
- **filesystem**: 文件操作 (`read_file`, `write_file`, `list_directory`)。
- **shrimp-task-manager**: 任务规划与执行 (`plan_task`, `execute_task`).

## ⚡ 智能调度策略
### 1. 任务预评估 (5%)
- **复杂度评分**（1-10分）：
  - **1-2分**：快速模式，仅L0工具+MCP单工具，<5秒响应。
  - **3-5分**：标准模式，L0+部分L1工具（MCP优先），<15秒响应。
  - **6-10分**：深度模式，L0+全L1工具矩阵，<60秒响应。
- **MCP优先**：问题场景匹配MCP工具，普通工具仅作为补充。

### 2. 协同执行 (80%)
- **并行激活**：MCP工具（如`tavily`、`filesystem`）并行运行，合并输出。
- **强制MCP优先**：任务启动时优先选择MCP工具，普通工具需明确需求。
- **实时监控**：`mcp-feedback-enhanced`跟踪执行状态，异常即中止。

### 3. 反馈优化 (15%)
- **mcp-feedback-enhanced**：收集用户满意度，记录工具效果。
- **knowledge-graph-memory**：存储成功工具组合，优化未来调用。
- **动态调整**：根据反馈更新工具优先级和组合策略。

## 🎯 具体实施建议
1. **MCP优先决策树**
   - 问题识别 → 映射MCP工具 → 自动激活。
   - 失败回退 → 普通工具补充 → 效果评估。
2. **反馈驱动优化**
   - 每次协作后收集用户满意度。
   - 建立工具选择效果数据库。
   - 持续优化激活策略。
3. **协作模式记忆**
   - 记录成功工具组合。
   - 识别用户偏好和习惯。
   - 提供预测性工具推荐。

## 🔄 持续优化机制
通过矛盾转化，将工具选择矛盾转化为协作效率提升机会：
- **正向循环**：用户反馈 → 优化策略 → 更好体验 → 更多反馈。
- **目标**：持续提升协作质量，强化MCP工具优先级。