#!/usr/bin/env python3
"""
简单的API迁移验证测试
"""

import json
import sys

import requests


def test_articles_api():
    """测试文章API是否正常工作"""
    base_url = "http://localhost:8000"
    
    try:
        # 测试获取文章列表
        response = requests.get(f"{base_url}/api/v1/articles/")
        print(f"GET /api/v1/articles/ - Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"完整响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            # 检查middleware包装的格式: {"status": "success", "data": ...}
            if data.get("status") == "success":
                print("✅ 文章列表API正常工作")
                print(f"返回数据结构: {list(data.keys())}")
                if "data" in data and isinstance(data["data"], dict) and "items" in data["data"]:
                    print(f"返回文章数量: {len(data['data']['items'])}")
                    print(f"分页信息: 第{data['data'].get('page', 1)}页，每页{data['data'].get('page_size', 10)}条")
            else:
                print(f"❌ API返回错误，状态: {data.get('status')}")
                print(f"错误信息: {data.get('data', {}).get('message', '无错误信息')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"错误内容: {response.text}")
            
        # 测试获取单个文章（假设ID为1）
        response = requests.get(f"{base_url}/api/v1/articles/1")
        print(f"\nGET /api/v1/articles/1 - Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "success":
                print("✅ 单个文章API正常工作")
                print(f"文章标题: {data.get('data', {}).get('title', '未知')}")
            else:
                print(f"❌ API返回错误: {data.get('data', {}).get('message', '未知错误')}")
        elif response.status_code == 404:
            print("ℹ️  文章不存在（正常情况）")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"错误内容: {response.text}")
            
        # 测试带统计信息的文章
        response = requests.get(f"{base_url}/api/v1/articles/1?include_stats=true")
        print(f"\nGET /api/v1/articles/1?include_stats=true - Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "success":
                print("✅ 带统计信息的文章API正常工作")
                article_data = data.get("data", {})
                if article_data.get("stats"):
                    print(f"统计信息: 访问{article_data['stats'].get('visit_count', 0)}次")
                else:
                    print("未包含统计信息（可能是因为文章不存在）")
            else:
                print(f"❌ API返回错误: {data.get('data', {}).get('message', '未知错误')}")
        elif response.status_code == 404:
            print("ℹ️  文章不存在（正常情况）")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"错误内容: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务器正在运行")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False
        
    return True

if __name__ == "__main__":
    print("开始测试API迁移...")
    success = test_articles_api()
    if success:
        print("\n✅ API迁移测试完成")
    else:
        print("\n❌ API迁移测试失败")
        sys.exit(1)