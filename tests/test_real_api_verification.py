#!/usr/bin/env python3
"""
验证实际API修复效果
测试批量获取文章接口是否能正确返回公开文章
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.article_permission_service import article_permission_service
from app.core.permission_system import Permission, ResourceType, Action, Scope
from typing import Optional

class MockDB:
    """模拟数据库会话"""
    pass

class MockArticle:
    """模拟文章对象"""
    def __init__(self, id: int, is_published: bool = True, is_approved: bool = True, author_id: int = 1):
        self.id = id
        self.is_published = is_published
        self.is_approved = is_approved
        self.author_id = author_id

class MockCrud:
    """模拟CRUD操作"""
    @staticmethod
    async def get_multi_by_ids(db, ids):
        # 模拟数据库中的文章
        all_articles = {
            1: MockArticle(1, True, True, 1),
            3: Mock<PERSON>rt<PERSON>(3, True, True, 2), 
            4: MockArticle(4, True, True, 1),
            7: MockArticle(7, True, True, 3),
            9: MockArticle(9, False, False, 1),  # 私有文章
            14: MockArticle(14, True, True, 2),
            15: MockArticle(15, True, True, 1),
            19: MockArticle(19, True, True, 3),
            21: MockArticle(21, True, True, 2),
            22: MockArticle(22, True, True, 1),
        }
        return [all_articles[id] for id in ids if id in all_articles]

# 替换实际的crud模块
import app.crud
app.crud.article = MockCrud()

async def test_filter_accessible_articles():
    """测试过滤可访问文章功能"""
    print("=== 测试实际API修复效果 ===")
    
    # 测试数据 - 与用户报告的相同
    article_ids = [4, 9, 7, 15, 19, 21, 14, 3, 22, 1]
    print(f"请求的文章ID: {article_ids}")
    
    # 测试未登录用户
    print("\n1. 测试未登录用户访问:")
    try:
        accessible_articles = await article_permission_service.filter_accessible_articles(
            db=MockDB(), user=None, article_ids=article_ids
        )
        print(f"可访问的文章ID: {accessible_articles}")
        
        if accessible_articles:
            print("✅ 修复成功！未登录用户现在可以访问公开文章")
            print(f"可访问文章数量: {len(accessible_articles)}")
            
            # 验证返回的都是公开文章
            expected_public_ids = [1, 3, 4, 7, 14, 15, 19, 21, 22]  # 排除私有文章9
            if set(accessible_articles) == set(expected_public_ids):
                print("✅ 返回的文章ID正确，都是公开文章")
            else:
                print(f"⚠️  返回的文章ID不完全正确，期望: {expected_public_ids}")
        else:
            print("❌ 修复失败！未登录用户仍然无法访问公开文章")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return len(accessible_articles) > 0 if 'accessible_articles' in locals() else False

async def test_permission_check_directly():
    """直接测试权限检查逻辑"""
    print("\n2. 直接测试权限检查逻辑:")
    
    # 创建一个公开文章
    public_article = MockArticle(1, True, True, 1)
    
    try:
        # 测试未登录用户对公开文章的访问权限
        has_access = await article_permission_service.check_article_access(
            db=MockDB(), content=public_article, current_user=None
        )
        
        print(f"未登录用户访问公开文章权限: {has_access}")
        
        if has_access:
            print("✅ 权限检查修复成功！")
        else:
            print("❌ 权限检查仍有问题")
            
        return has_access
        
    except Exception as e:
        print(f"❌ 权限检查过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("实际API修复验证测试")
    print("=" * 50)
    
    # 测试1: 过滤可访问文章
    filter_success = await test_filter_accessible_articles()
    
    # 测试2: 直接权限检查
    permission_success = await test_permission_check_directly()
    
    print("\n=== 修复验证总结 ===")
    print(f"过滤可访问文章测试: {'✅ 通过' if filter_success else '❌ 失败'}")
    print(f"直接权限检查测试: {'✅ 通过' if permission_success else '❌ 失败'}")
    
    if filter_success and permission_success:
        print("\n🎉 所有测试通过！批量获取文章接口修复成功！")
        print("\n修复详情:")
        print("- 问题: PermissionChecker.check_permission调用时未传入resource参数")
        print("- 修复: 在ArticlePermissionService.check_article_access方法中")
        print("  为公开文章权限检查传入content作为resource参数")
        print("- 效果: 未登录用户现在可以正确访问公开文章")
    else:
        print("\n❌ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    asyncio.run(main())