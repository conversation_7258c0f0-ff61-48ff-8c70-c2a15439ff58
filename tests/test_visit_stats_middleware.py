"""测试访问统计中间件"""

import asyncio
import random

import httpx

# API基础URL
BASE_URL = "http://localhost:8000"

# 测试视频和文章ID范围
VIDEO_ID_RANGE = (1, 10)
ARTICLE_ID_RANGE = (1, 10)


async def test_visit_stats():
    """测试访问统计中间件"""
    print("开始测试访问统计中间件...")
    
    # 创建异步HTTP客户端
    async with httpx.AsyncClient() as client:
        # 测试视频访问统计
        video_id = random.randint(*VIDEO_ID_RANGE)
        print(f"\n测试视频ID: {video_id}")
        
        # 获取初始访问次数
        try:
            response = await client.get(f"{BASE_URL}/api/v1/videos/{video_id}/with-stats")
            if response.status_code == 200:
                data = response.json()["data"]
                initial_visit_count = data.get("visit_count", 0)
                print(f"初始访问次数: {initial_visit_count}")
            else:
                print(f"获取视频失败: {response.status_code} - {response.text}")
                return
        except Exception as e:
            print(f"请求异常: {e}")
            return
        
        # 多次访问视频详情页
        visit_count = 3
        for i in range(visit_count):
            try:
                response = await client.get(f"{BASE_URL}/api/v1/videos/{video_id}")
                if response.status_code == 200:
                    print(f"访问视频成功 ({i+1}/{visit_count})")
                else:
                    print(f"访问视频失败: {response.status_code}")
            except Exception as e:
                print(f"请求异常: {e}")
            # 短暂延迟，避免请求过快
            await asyncio.sleep(0.5)
        
        # 检查访问次数是否增加
        try:
            response = await client.get(f"{BASE_URL}/api/v1/videos/{video_id}/with-stats")
            if response.status_code == 200:
                data = response.json()["data"]
                final_visit_count = data.get("visit_count", 0)
                print(f"最终访问次数: {final_visit_count}")
                print(f"增加的访问次数: {final_visit_count - initial_visit_count}")
                if final_visit_count >= initial_visit_count + visit_count:
                    print("视频访问统计测试通过!")
                else:
                    print("视频访问统计测试失败: 访问次数未正确增加")
            else:
                print(f"获取视频失败: {response.status_code}")
        except Exception as e:
            print(f"请求异常: {e}")
        
        # 测试文章访问统计
        article_id = random.randint(*ARTICLE_ID_RANGE)
        print(f"\n测试文章ID: {article_id}")
        
        # 获取初始访问次数
        try:
            response = await client.get(f"{BASE_URL}/api/v1/articles/{article_id}?include_stats=true")
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    data = result["data"]
                    initial_visit_count = data.get("visit_count", 0)
                else:
                    print(f"API错误: {result.get('message', 'Unknown error')}")
                    return
                print(f"初始访问次数: {initial_visit_count}")
            else:
                print(f"获取文章失败: {response.status_code} - {response.text}")
                return
        except Exception as e:
            print(f"请求异常: {e}")
            return
        
        # 多次访问文章详情页
        visit_count = 3
        for i in range(visit_count):
            try:
                response = await client.get(f"{BASE_URL}/api/v1/articles/{article_id}")
                if response.status_code == 200:
                    print(f"访问文章成功 ({i+1}/{visit_count})")
                else:
                    print(f"访问文章失败: {response.status_code}")
            except Exception as e:
                print(f"请求异常: {e}")
            # 短暂延迟，避免请求过快
            await asyncio.sleep(0.5)
        
        # 检查访问次数是否增加
        try:
            response = await client.get(f"{BASE_URL}/api/v1/articles/{article_id}?include_stats=true")
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    data = result["data"]
                    final_visit_count = data.get("visit_count", 0)
                else:
                    print(f"API错误: {result.get('message', 'Unknown error')}")
                    return
                print(f"最终访问次数: {final_visit_count}")
                print(f"增加的访问次数: {final_visit_count - initial_visit_count}")
                if final_visit_count >= initial_visit_count + visit_count:
                    print("文章访问统计测试通过!")
                else:
                    print("文章访问统计测试失败: 访问次数未正确增加")
            else:
                print(f"获取文章失败: {response.status_code}")
        except Exception as e:
            print(f"请求异常: {e}")


if __name__ == "__main__":
    asyncio.run(test_visit_stats())