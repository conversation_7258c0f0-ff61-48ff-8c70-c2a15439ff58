#!/usr/bin/env python3

import asyncio
import json

import httpx


async def test_api():
    """简单的API测试"""
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            # 测试基本的用户文章列表API
            url = "http://localhost:8001/api/v1/articles/users/1/articles"
            print(f"测试URL: {url}")
            
            response = await client.get(url)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"成功响应: {json.dumps(data, indent=2, ensure_ascii=False)[:500]}...")
            else:
                print(f"错误响应: {response.text}")
                
        except Exception as e:
            print(f"请求异常: {type(e).__name__}: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_api())