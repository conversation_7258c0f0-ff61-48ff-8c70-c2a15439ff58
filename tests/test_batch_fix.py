#!/usr/bin/env python3
"""
测试批量获取文章修复
验证未登录用户是否能访问公开文章
"""

import asyncio
from typing import Optional, List
from dataclasses import dataclass

# 模拟核心组件
@dataclass
class MockArticle:
    id: int
    title: str
    is_published: bool
    is_approved: bool
    author_id: int

@dataclass
class MockUser:
    id: int
    username: str
    is_superuser: bool = False
    role_id: Optional[int] = None

class MockPermissionChecker:
    @staticmethod
    async def check_permission(db, user, permission, resource=None):
        # 模拟权限检查逻辑
        if permission.scope.value == "PUBLIC" and resource:
            # 检查公开资源权限
            is_public = getattr(resource, "is_published", False) and getattr(resource, "is_approved", False)
            if is_public and permission.action.value == "READ":
                return True
        return False

class MockPermission:
    def __init__(self, resource_type, action, scope):
        self.resource_type = resource_type
        self.action = action
        self.scope = scope

class MockResourceType:
    ARTICLE = "ARTICLE"

class MockAction:
    READ = type('Action', (), {'value': 'READ'})()

class MockScope:
    PUBLIC = type('Scope', (), {'value': 'PUBLIC'})()

class MockArticlePermissionService:
    @staticmethod
    async def check_article_access(db, content, current_user=None):
        """检查文章访问权限（修复后的版本）"""
        if not content:
            return False

        # 检查对公开文章的读取权限（修复：传入content作为resource参数）
        if content.is_published and content.is_approved:
            permission = MockPermission(MockResourceType.ARTICLE, MockAction.READ, MockScope.PUBLIC)
            if await MockPermissionChecker.check_permission(db, current_user, permission, content):
                return True

        return False

    @staticmethod
    async def filter_accessible_articles(db, user, article_ids):
        """过滤用户可访问的文章ID列表"""
        if not article_ids:
            return []
        
        # 模拟从数据库获取文章
        articles = [
            MockArticle(id=1, title="公开文章1", is_published=True, is_approved=True, author_id=1),
            MockArticle(id=3, title="公开文章3", is_published=True, is_approved=True, author_id=2),
            MockArticle(id=4, title="公开文章4", is_published=True, is_approved=True, author_id=1),
            MockArticle(id=7, title="公开文章7", is_published=True, is_approved=True, author_id=3),
            MockArticle(id=9, title="私有文章9", is_published=False, is_approved=False, author_id=1),
            MockArticle(id=14, title="公开文章14", is_published=True, is_approved=True, author_id=2),
            MockArticle(id=15, title="公开文章15", is_published=True, is_approved=True, author_id=1),
            MockArticle(id=19, title="公开文章19", is_published=True, is_approved=True, author_id=3),
            MockArticle(id=21, title="公开文章21", is_published=True, is_approved=True, author_id=2),
            MockArticle(id=22, title="公开文章22", is_published=True, is_approved=True, author_id=1),
        ]
        
        # 过滤出请求的文章
        requested_articles = [a for a in articles if a.id in article_ids]
        
        accessible_ids = []
        for article in requested_articles:
            # 检查每篇文章的访问权限
            if await MockArticlePermissionService.check_article_access(db, article, user):
                accessible_ids.append(article.id)
                
        return accessible_ids

async def test_batch_article_fix():
    """测试批量文章访问修复"""
    print("=== 测试批量文章访问修复 ===")
    
    # 测试数据
    article_ids = [4, 9, 7, 15, 19, 21, 14, 3, 22, 1]
    
    # 测试未登录用户访问
    print("\n1. 测试未登录用户访问:")
    print(f"请求的文章ID: {article_ids}")
    
    accessible_articles = await MockArticlePermissionService.filter_accessible_articles(
        db=None, user=None, article_ids=article_ids
    )
    
    print(f"可访问的文章ID: {accessible_articles}")
    
    if accessible_articles:
        print("✅ 修复成功！未登录用户现在可以访问公开文章")
        print(f"可访问文章数量: {len(accessible_articles)}")
    else:
        print("❌ 修复失败！未登录用户仍然无法访问公开文章")
    
    # 测试登录用户访问
    print("\n2. 测试登录用户访问:")
    user = MockUser(id=1, username="test_user")
    
    accessible_articles_logged_in = await MockArticlePermissionService.filter_accessible_articles(
        db=None, user=user, article_ids=article_ids
    )
    
    print(f"登录用户可访问的文章ID: {accessible_articles_logged_in}")
    
    return accessible_articles

async def main():
    """主函数"""
    print("批量获取文章权限修复测试")
    print("=" * 50)
    
    result = await test_batch_article_fix()
    
    print("\n=== 修复总结 ===")
    print("问题: 未登录用户无法访问公开文章")
    print("原因: PermissionChecker.check_permission调用时未传入resource参数")
    print("修复: 在check_article_access方法中传入content作为resource参数")
    print(f"结果: {'修复成功' if result else '修复失败'}")

if __name__ == "__main__":
    asyncio.run(main())