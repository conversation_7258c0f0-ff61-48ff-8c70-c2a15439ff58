import requests


def test_api():
    url = "http://localhost:8001/api/v1/articles/users/1/articles"

    try:
        response = requests.get(url)
        print(f"测试URL: {url}")
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print("\n=== API响应成功 ===")
            print(f"总数: {data['data']['total']}")

            if data["data"]["items"]:
                first_item = data["data"]["items"][0]
                print("\n=== 第一个文章项目 ===")
                print(f"ID: {first_item['id']}")
                print(f"标题: {first_item['title']}")
                print(f"作者: {first_item.get('author', {}).get('username', 'N/A')}")
                print(
                    f"分类: {first_item.get('category', {}).get('name', 'N/A') if first_item.get('category') else 'None'}"
                )
                tags = first_item.get("tags") or []
                print(f"标签数量: {len(tags)}")
                if tags:
                    tag_names = [tag.get("name", "N/A") for tag in tags]
                    print(f"标签: {', '.join(tag_names)}")
                else:
                    print("标签: 无")

                # 检查统计信息
                if "stats" in first_item:
                    stats = first_item["stats"]
                    print("\n=== 统计信息 ===")
                    print(f"访问量: {stats.get('visit_count', 'N/A')}")
                    print(f"点赞数: {stats.get('like_count', 'N/A')}")
                    print(f"收藏数: {stats.get('favorite_count', 'N/A')}")

            print("\n=== 测试通过：MissingGreenlet错误已解决 ===")
        else:
            print(f"错误响应: {response.text}")

    except Exception as e:
        print(f"请求失败: {e}")


if __name__ == "__main__":
    test_api()
