"""
响应格式测试

测试API响应格式的一致性和正确性
"""

import pytest
from fastapi.testclient import TestClient

from app.main import app
from app.utils.response_validator import ResponseValidator, ResponseFormatChecker


class TestResponseFormat:
    """响应格式测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.client = TestClient(app)
        self.checker = ResponseFormatChecker(enabled=True)
    
    def test_health_endpoint_format(self):
        """测试健康检查端点的响应格式"""
        response = self.client.get("/health")
        assert response.status_code == 200
        
        # 健康检查端点不经过ResponseFormatterMiddleware
        data = response.json()
        assert "status" in data
        assert data["status"] == "healthy"
    
    def test_root_endpoint_format(self):
        """测试根端点的响应格式"""
        response = self.client.get("/")
        assert response.status_code == 200
        
        # 根端点不在/api/v1路径下，不经过ResponseFormatterMiddleware
        data = response.json()
        assert "message" in data
    
    def test_api_success_response_format(self):
        """测试API成功响应格式"""
        # 这里需要一个实际的API端点来测试
        # 由于需要认证，我们先测试公开的端点
        
        # 测试分类树接口（公开接口）
        response = self.client.get("/api/v1/categories/tree")
        assert response.status_code == 200
        
        data = response.json()
        
        # 验证是否符合标准成功响应格式
        assert self.checker.check_response(data, "/api/v1/categories/tree")
        
        # 验证具体格式
        assert ResponseValidator.validate_success_response(data)
        assert data["status"] == "success"
        assert "data" in data
    
    def test_api_error_response_format(self):
        """测试API错误响应格式"""
        # 测试一个不存在的端点
        response = self.client.get("/api/v1/nonexistent")
        assert response.status_code == 404
        
        data = response.json()
        
        # 验证错误响应格式
        assert ResponseValidator.validate_error_response(data)
        assert data["status"] == "error"
        assert "message" in data
    
    def test_pagination_response_format(self):
        """测试分页响应格式"""
        # 测试文章列表接口
        response = self.client.get("/api/v1/articles/?size=5")
        
        if response.status_code == 200:
            data = response.json()
            
            # 验证分页响应格式
            assert ResponseValidator.validate_pagination_response(data)
            assert data["status"] == "success"
            
            pagination_data = data["data"]
            assert "items" in pagination_data
            assert "has_next" in pagination_data
            assert "has_previous" in pagination_data
            assert isinstance(pagination_data["items"], list)
    
    def test_content_response_format(self):
        """测试内容响应格式"""
        # 首先获取文章列表
        response = self.client.get("/api/v1/articles/?size=1")
        
        if response.status_code == 200:
            data = response.json()
            
            if data["data"]["items"]:
                # 获取第一篇文章的详情
                article_id = data["data"]["items"][0]["id"]
                detail_response = self.client.get(f"/api/v1/articles/{article_id}")
                
                if detail_response.status_code == 200:
                    detail_data = detail_response.json()
                    
                    # 验证内容响应格式
                    assert ResponseValidator.validate_content_response(detail_data)
                    assert detail_data["status"] == "success"
                    
                    content_data = detail_data["data"]
                    assert "id" in content_data
                    assert "title" in content_data
                    assert "author" in content_data
    
    def test_validation_error_format(self):
        """测试验证错误响应格式"""
        # 发送无效的请求参数
        response = self.client.get("/api/v1/articles/?size=invalid")
        assert response.status_code == 422
        
        data = response.json()
        
        # 验证422错误的响应格式
        assert data["status"] == "error"
        assert "message" in data
        assert "errors" in data
    
    def test_response_consistency(self):
        """测试响应一致性"""
        responses = []
        
        # 收集多个响应
        endpoints = [
            "/api/v1/categories/tree",
            "/api/v1/articles/?size=1",
        ]
        
        for endpoint in endpoints:
            response = self.client.get(endpoint)
            if response.status_code == 200:
                responses.append(response.json())
        
        if responses:
            # 验证响应一致性
            results = ResponseValidator.validate_response_consistency(responses)
            
            assert results["invalid_responses"] == 0, f"发现无效响应: {results['format_issues']}"
            assert len(results["consistency_issues"]) == 0, f"发现一致性问题: {results['consistency_issues']}"
    
    def test_middleware_processing(self):
        """测试中间件处理"""
        # 测试成功响应是否被正确包装
        response = self.client.get("/api/v1/categories/tree")
        assert response.status_code == 200
        
        data = response.json()
        
        # 验证中间件是否正确包装了响应
        assert "status" in data
        assert "data" in data
        assert data["status"] == "success"
        
        # 验证没有双重包装
        inner_data = data["data"]
        if isinstance(inner_data, dict):
            # 内层数据不应该再有status字段（除非是特殊情况）
            if "status" in inner_data:
                # 如果内层有status，应该不是"success"或"error"
                assert inner_data["status"] not in ["success", "error"], "检测到可能的双重包装"


class TestResponseValidatorUnit:
    """响应验证器单元测试"""
    
    def test_valid_success_response(self):
        """测试有效的成功响应"""
        response = {
            "status": "success",
            "data": {"id": 1, "title": "测试"}
        }
        assert ResponseValidator.validate_success_response(response)
    
    def test_invalid_success_response(self):
        """测试无效的成功响应"""
        response = {
            "status": "success"
            # 缺少data字段
        }
        assert not ResponseValidator.validate_success_response(response)
    
    def test_valid_error_response(self):
        """测试有效的错误响应"""
        response = {
            "status": "error",
            "message": "错误信息"
        }
        assert ResponseValidator.validate_error_response(response)
    
    def test_invalid_error_response(self):
        """测试无效的错误响应"""
        response = {
            "status": "error"
            # 缺少message字段
        }
        assert not ResponseValidator.validate_error_response(response)
    
    def test_valid_pagination_response(self):
        """测试有效的分页响应"""
        response = {
            "status": "success",
            "data": {
                "items": [],
                "has_next": False,
                "has_previous": False,
                "next_cursor": None,
                "previous_cursor": None
            }
        }
        assert ResponseValidator.validate_pagination_response(response)
    
    def test_invalid_pagination_response(self):
        """测试无效的分页响应"""
        response = {
            "status": "success",
            "data": {
                "items": []
                # 缺少has_next和has_previous字段
            }
        }
        assert not ResponseValidator.validate_pagination_response(response)


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
