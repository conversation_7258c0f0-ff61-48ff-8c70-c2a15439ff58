#!/usr/bin/env python3
"""
测试文章状态筛选功能
验证PUBLISHED_PENDING和PUBLISHED_REJECTED状态的正确区分
"""

import asyncio
import json

import httpx


class ArticleStatusTester:
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.client = httpx.AsyncClient()
    
    async def test_article_status_filtering(self):
        """测试文章状态筛选功能"""
        print("=== 文章状态筛选功能测试 ===")
        
        # 测试不同状态的文章列表
        statuses = [
            ("all", "所有文章"),
            ("draft", "草稿文章"),
            ("published_approved", "已发布且已审核通过"),
            ("published_pending", "已发布但待审核"),
            ("published_rejected", "已发布但审核被拒绝")
        ]
        
        # 假设用户ID为1
        user_id = 1
        
        for status_value, status_desc in statuses:
            print(f"\n--- 测试 {status_desc} ({status_value}) ---")
            
            try:
                # 测试不包含审核信息的请求
                url = f"{self.base_url}/api/v1/articles/users/{user_id}/articles"
                params = {
                    "status": status_value,
                    "include_stats": True,
                    "include_review": False,
                    "skip": 0,
                    "limit": 10
                }
                
                response = await self.client.get(url, params=params)
                print(f"状态码: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    if "data" in data:
                        items = data["data"].get("items", [])
                        total = data["data"].get("total", 0)
                        print(f"总数: {total}")
                        print(f"返回条数: {len(items)}")
                        
                        # 显示前3篇文章的基本信息
                        for i, article in enumerate(items[:3]):
                            print(f"  文章{i+1}: {article.get('title', 'N/A')} - 发布状态: {article.get('is_published', 'N/A')} - 审核状态: {article.get('is_approved', 'N/A')}")
                    else:
                        print(f"响应数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
                else:
                    print(f"请求失败: {response.text}")
                    
            except Exception as e:
                print(f"请求异常: {type(e).__name__}: {e}")
                import traceback
                traceback.print_exc()
        
        # 测试包含审核信息的请求
        print("\n--- 测试包含审核信息的请求 ---")
        try:
            url = f"{self.base_url}/api/v1/articles/users/{user_id}/articles"
            params = {
                "status": "published_pending",
                "include_stats": True,
                "include_review": True,
                "skip": 0,
                "limit": 5
            }
            
            response = await self.client.get(url, params=params)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if "data" in data:
                    items = data["data"].get("items", [])
                    total = data["data"].get("total", 0)
                    print(f"待审核文章总数: {total}")
                    print(f"返回条数: {len(items)}")
                    
                    # 显示审核信息
                    for i, article in enumerate(items):
                        review_info = article.get("review", {})
                        print(f"  文章{i+1}: {article.get('title', 'N/A')}")
                        print(f"    审核状态: {review_info.get('status', 'N/A')}")
                        print(f"    审核评论: {review_info.get('comment', 'N/A')}")
                        print(f"    审核时间: {review_info.get('reviewed_at', 'N/A')}")
                else:
                    print(f"响应数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
            else:
                print(f"请求失败: {response.text}")
                
        except Exception as e:
            print(f"请求异常: {type(e).__name__}: {e}")
            import traceback
            traceback.print_exc()
    
    async def close(self):
        """关闭HTTP客户端"""
        await self.client.aclose()


async def main():
    """主函数"""
    tester = ArticleStatusTester()
    
    try:
        await tester.test_article_status_filtering()
    finally:
        await tester.close()
    
    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    asyncio.run(main())