# API迁移指南 - 文章列表API重构

## 🚨 重要通知

原有的 `/my` 和 `/my/with-review` API端点已被**删除**，请立即迁移到新的统一API。

## 📋 迁移对照表

### 1. 获取当前用户文章列表（包含统计信息）

#### 旧API（已删除）
```bash
GET /api/v1/articles/my?status=all&skip=0&limit=100
```

#### 新API
```bash
GET /api/v1/articles/users/{current_user_id}/articles?status=all&skip=0&limit=100&include_stats=true
```

### 2. 获取当前用户文章列表（包含审核信息）

#### 旧API（已删除）
```bash
GET /api/v1/articles/my/with-review?status=all&skip=0&limit=100
```

#### 新API
```bash
GET /api/v1/articles/users/{current_user_id}/articles?status=all&skip=0&limit=100&include_review=true
```

### 3. 获取当前用户文章列表（包含统计和审核信息）

#### 旧API（无对应接口）
```bash
# 旧版本需要两次调用
```

#### 新API
```bash
GET /api/v1/articles/users/{current_user_id}/articles?status=all&include_stats=true&include_review=true
```

## 🔄 参数映射

| 旧参数 | 新参数 | 说明 |
|--------|--------|------|
| `status` | `status` | 状态筛选（仅作者本人可用） |
| `skip` | `skip` | 分页跳过数量 |
| `limit` | `limit` | 分页限制数量 |
| - | `include_stats` | 是否包含统计信息（默认true） |
| - | `include_review` | 是否包含审核信息（默认false） |

## 🔐 权限变化

### 旧版本
- `/my` - 只能访问当前用户的文章
- `/my/with-review` - 只能访问当前用户的文章

### 新版本
- `/users/{user_id}/articles` - 支持访问任意用户的文章，但有权限控制：
  - **作者本人**：可访问所有状态的文章，可使用所有参数
  - **其他用户**：只能访问已发布且已审核的文章，`status`和`include_review`参数被忽略
  - **游客**：只能访问已发布且已审核的文章

## 📝 代码迁移示例

### JavaScript/TypeScript

#### 旧代码
```javascript
// 获取我的文章
const response = await fetch('/api/v1/articles/my?status=all');
const data = await response.json();

// 获取我的文章（包含审核信息）
const reviewResponse = await fetch('/api/v1/articles/my/with-review?status=all');
const reviewData = await reviewResponse.json();
```

#### 新代码
```javascript
// 获取我的文章（包含统计信息）
const response = await fetch(`/api/v1/articles/users/${currentUserId}/articles?status=all&include_stats=true`);
const data = await response.json();

// 获取我的文章（包含统计和审核信息）
const fullResponse = await fetch(`/api/v1/articles/users/${currentUserId}/articles?status=all&include_stats=true&include_review=true`);
const fullData = await fullResponse.json();
```

### Python

#### 旧代码
```python
import requests

# 获取我的文章
response = requests.get('/api/v1/articles/my?status=all', headers=headers)
data = response.json()

# 获取我的文章（包含审核信息）
review_response = requests.get('/api/v1/articles/my/with-review?status=all', headers=headers)
review_data = review_response.json()
```

#### 新代码
```python
import requests

# 获取我的文章（包含统计信息）
response = requests.get(f'/api/v1/articles/users/{current_user_id}/articles?status=all&include_stats=true', headers=headers)
data = response.json()

# 获取我的文章（包含统计和审核信息）
full_response = requests.get(f'/api/v1/articles/users/{current_user_id}/articles?status=all&include_stats=true&include_review=true', headers=headers)
full_data = full_response.json()
```

## 🎯 新功能优势

1. **统一接口**：一个API支持多种数据组合
2. **权限控制**：支持访问其他用户的公开文章
3. **灵活配置**：可选择性加载统计信息和审核信息
4. **性能优化**：减少API调用次数
5. **扩展性**：为未来功能扩展提供基础

## ⚠️ 注意事项

1. **用户ID获取**：需要从认证信息中获取当前用户ID
2. **权限检查**：确保正确处理权限不足的情况
3. **参数验证**：新API的参数验证更严格
4. **响应格式**：响应数据结构有所变化，请检查字段映射

## 🔧 响应格式变化

### 统一响应格式
```json
{
  "total": 10,
  "items": [
    {
      "id": 1,
      "title": "文章标题",
      "description": "文章描述",
      "author": {
        "id": 1,
        "username": "作者用户名"
      },
      "like_count": 10,           // 统计信息（include_stats=true时）
      "favorite_count": 5,        // 统计信息（include_stats=true时）
      "is_liked_by_user": false,  // 统计信息（include_stats=true时）
      "is_favorited_by_user": false, // 统计信息（include_stats=true时）
      "review": {                 // 审核信息（include_review=true时）
        "id": 1,
        "status": "approved",
        "comment": "审核通过"
      }
    }
  ]
}
```

## 🚀 迁移步骤

1. **识别调用点**：找到所有使用旧API的代码位置
2. **获取用户ID**：确保能够获取当前用户ID
3. **更新URL**：将API路径更新为新格式
4. **调整参数**：根据需要添加`include_stats`和`include_review`参数
5. **测试验证**：确保新API调用正常工作
6. **处理错误**：添加适当的错误处理逻辑

## 📞 技术支持

如果在迁移过程中遇到问题，请：

1. 查看 `UNIFIED_ARTICLE_API_GUIDE.md` 获取详细API文档
2. 检查权限配置是否正确
3. 验证用户认证状态
4. 确认API参数格式

## 🎉 迁移完成检查清单

- [ ] 所有旧API调用已更新
- [ ] 用户ID获取逻辑已实现
- [ ] 权限错误处理已添加
- [ ] 响应数据解析已更新
- [ ] 功能测试已通过
- [ ] 性能测试已验证

---

**迁移截止时间**: 请尽快完成迁移，旧API已被删除，继续使用将导致404错误。
