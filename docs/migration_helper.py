#!/usr/bin/env python3
"""
文章API迁移助手脚本

此脚本帮助识别和迁移项目中使用旧文章API的代码。
"""

import os
import re
import sys
from pathlib import Path

# 旧API模式和对应的新API
API_MIGRATIONS = {
    # 获取文章详情
    r"/articles/(\d+)(?!/unified)": r"/articles/unified/\1",
    r"/articles/(\d+)/with-stats": r"/articles/unified/\1?include_stats=true",
    
    # 获取文章列表
    r"/articles/with-stats": r"/articles/unified?include_stats=true",
    r"/articles/(\d+)/with-stats": r"/articles/unified?category_id=\1&include_stats=true",
    
    # 创建文章
    r"POST /articles(?!/unified)": r"POST /articles/unified",
}

# 需要检查的文件扩展名
FILE_EXTENSIONS = [".py", ".js", ".ts", ".jsx", ".tsx", ".md", ".json"]

# 排除的目录
EXCLUDE_DIRS = {
    "__pycache__", ".git", "node_modules", ".pytest_cache", 
    "venv", "env", ".venv", "dist", "build"
}

def find_files_to_check(root_dir: str) -> list[Path]:
    """查找需要检查的文件"""
    files = []
    root_path = Path(root_dir)
    
    for file_path in root_path.rglob("*"):
        if file_path.is_file() and file_path.suffix in FILE_EXTENSIONS:
            # 检查是否在排除目录中
            if not any(exclude_dir in file_path.parts for exclude_dir in EXCLUDE_DIRS):
                files.append(file_path)
    
    return files

def check_file_for_old_apis(file_path: Path) -> list[tuple[int, str, str]]:
    """检查文件中的旧API使用"""
    issues = []
    
    try:
        with open(file_path, encoding="utf-8") as f:
            lines = f.readlines()
        
        for line_num, line in enumerate(lines, 1):
            line_content = line.strip()
            
            # 检查各种旧API模式
            patterns = [
                (r"/articles/\d+/with-stats", "使用统一API: /articles/unified/{id}?include_stats=true"),
                (r"/articles/with-stats", "使用统一API: /articles/unified?include_stats=true"),
                (r"/articles/\d+(?!/unified)", "使用统一API: /articles/unified/{id}"),
                (r"POST.*?/articles(?!/unified)", "使用统一API: POST /articles/unified"),
            ]
            
            for pattern, suggestion in patterns:
                if re.search(pattern, line_content):
                    issues.append((line_num, line_content, suggestion))
    
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {e}")
    
    return issues

def generate_migration_report(root_dir: str) -> dict[str, list[tuple[int, str, str]]]:
    """生成迁移报告"""
    print("正在扫描项目文件...")
    files = find_files_to_check(root_dir)
    print(f"找到 {len(files)} 个文件需要检查")
    
    report = {}
    total_issues = 0
    
    for file_path in files:
        issues = check_file_for_old_apis(file_path)
        if issues:
            report[str(file_path)] = issues
            total_issues += len(issues)
    
    print(f"\n扫描完成！找到 {total_issues} 个需要迁移的API调用")
    return report

def print_migration_report(report: dict[str, list[tuple[int, str, str]]]):
    """打印迁移报告"""
    if not report:
        print("\n🎉 太棒了！没有找到需要迁移的旧API调用。")
        return
    
    print("\n" + "="*80)
    print("📋 文章API迁移报告")
    print("="*80)
    
    for file_path, issues in report.items():
        print(f"\n📁 文件: {file_path}")
        print("-" * 60)
        
        for line_num, line_content, suggestion in issues:
            print(f"  第 {line_num} 行: {line_content}")
            print(f"  💡 建议: {suggestion}")
            print()

def generate_migration_script(report: dict[str, list[tuple[int, str, str]]]) -> str:
    """生成迁移脚本"""
    script_lines = [
        "#!/bin/bash",
        "# 自动生成的API迁移脚本",
        "# 请在执行前仔细检查每个替换操作",
        "",
        "echo '开始执行API迁移...'",
        ""
    ]
    
    # 生成sed命令来替换常见模式
    replacements = [
        (r"/articles/([0-9]+)/with-stats", r"/articles/unified/\1?include_stats=true"),
        (r"/articles/with-stats", r"/articles/unified?include_stats=true"),
        (r"POST /articles(?!/unified)", r"POST /articles/unified"),
    ]
    
    for old_pattern, new_pattern in replacements:
        script_lines.append(f"# 替换: {old_pattern} -> {new_pattern}")
        script_lines.append(f"find . -type f \\( -name '*.py' -o -name '*.js' -o -name '*.ts' -o -name '*.md' \\) -exec sed -i 's|{old_pattern}|{new_pattern}|g' {{}} +")
        script_lines.append("")
    
    script_lines.extend([
        "echo '迁移完成！请检查修改的文件并测试功能。'",
        "echo '建议运行测试套件确保一切正常工作。'"
    ])
    
    return "\n".join(script_lines)

def main():
    """主函数"""
    if len(sys.argv) > 1:
        root_dir = sys.argv[1]
    else:
        root_dir = os.getcwd()
    
    print(f"正在检查目录: {root_dir}")
    
    # 生成报告
    report = generate_migration_report(root_dir)
    
    # 打印报告
    print_migration_report(report)
    
    # 生成迁移脚本
    if report:
        script_content = generate_migration_script(report)
        script_path = Path(root_dir) / "migrate_apis.sh"
        
        with open(script_path, "w", encoding="utf-8") as f:
            f.write(script_content)
        
        os.chmod(script_path, 0o755)
        print(f"\n📝 已生成迁移脚本: {script_path}")
        print("⚠️  请在执行前仔细检查脚本内容！")
    
    # 提供迁移指导
    print("\n" + "="*80)
    print("📚 迁移指导")
    print("="*80)
    print("1. 仔细检查上述报告中的每个API调用")
    print("2. 手动更新代码或使用生成的迁移脚本")
    print("3. 更新API响应处理逻辑以适配新的统一响应格式")
    print("4. 运行测试确保功能正常")
    print("5. 更新文档和API规范")
    print("\n详细迁移指南请参考: ARTICLES_API_MIGRATION_GUIDE.md")

if __name__ == "__main__":
    main()