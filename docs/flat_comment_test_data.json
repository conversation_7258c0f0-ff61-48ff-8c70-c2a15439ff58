{"扁平化评论API使用指南": {"原有API增强": {"文章评论": "GET /api/v1/comments/article/{article_id}", "视频评论": "GET /api/v1/comments/video/{video_id}", "新增参数": {"flat": "bool, 默认true, 是否返回扁平化结构", "max_level": "int, 默认10, 扁平化时的最大显示层级(0-10，10表示显示所有层级)"}}, "使用示例": {"嵌套结构(原有)": "GET /api/v1/comments/article/123?flat=false", "扁平化结构(新增)": "GET /api/v1/comments/article/123?flat=true&max_level=2", "所有层级扁平化(推荐)": "GET /api/v1/comments/article/123?flat=true&max_level=10", "仅顶层评论": "GET /api/v1/comments/article/123?flat=true&max_level=0", "顶层+一级回复": "GET /api/v1/comments/article/123?flat=true&max_level=1", "顶层+二级回复": "GET /api/v1/comments/article/123?flat=true&max_level=2", "按时间排序所有层级": "GET /api/v1/comments/article/123?flat=true&max_level=10&sort_by=created_at"}, "扁平化响应数据结构": {"items": [{"id": 1, "content": "这篇文章写得真不错！", "comment_type": "article", "article_id": 1, "video_id": null, "level": 0, "parent_id": null, "reply_to_id": null, "reply_to_user_id": null, "reply_to_user": null, "path": "1", "author_id": 101, "author": {"id": 101, "username": "张三", "nickname": "技术爱好者", "avatar": "https://example.com/avatar1.jpg"}, "like_count": 25, "is_liked": false, "reply_count": 3, "total_reply_count": 5, "is_visible": true, "created_at": "2024-01-15T10:30:00Z", "updated_at": "2024-01-15T10:30:00Z"}, {"id": 2, "content": "@张三 我也觉得，特别是第三部分的分析很到位", "comment_type": "article", "article_id": 1, "video_id": null, "level": 1, "parent_id": 1, "reply_to_id": 1, "reply_to_user_id": 101, "reply_to_user": {"id": 101, "username": "张三", "nickname": "技术爱好者", "avatar": "https://example.com/avatar1.jpg"}, "path": "1.2", "author_id": 102, "author": {"id": 102, "username": "李四", "nickname": "前端开发者", "avatar": "https://example.com/avatar2.jpg"}, "like_count": 12, "is_liked": true, "reply_count": 2, "total_reply_count": 2, "is_visible": true, "created_at": "2024-01-15T11:15:00Z", "updated_at": "2024-01-15T11:15:00Z"}, {"id": 3, "content": "@李四 确实，作者的思路很清晰", "comment_type": "article", "article_id": 1, "video_id": null, "level": 2, "parent_id": 2, "reply_to_id": 2, "reply_to_user_id": 102, "reply_to_user": {"id": 102, "username": "李四", "nickname": "前端开发者", "avatar": "https://example.com/avatar2.jpg"}, "path": "1.2.3", "author_id": 103, "author": {"id": 103, "username": "王五", "nickname": "产品经理", "avatar": "https://example.com/avatar3.jpg"}, "like_count": 8, "is_liked": false, "reply_count": 0, "total_reply_count": 0, "is_visible": true, "created_at": "2024-01-15T12:00:00Z", "updated_at": "2024-01-15T12:00:00Z"}, {"id": 4, "content": "@李四 我有不同的看法，第三部分可能还需要更深入的讨论", "comment_type": "article", "article_id": 1, "video_id": null, "level": 2, "parent_id": 2, "reply_to_id": 2, "reply_to_user_id": 102, "reply_to_user": {"id": 102, "username": "李四", "nickname": "前端开发者", "avatar": "https://example.com/avatar2.jpg"}, "path": "1.2.4", "author_id": 104, "author": {"id": 104, "username": "赵六", "nickname": "架构师", "avatar": "https://example.com/avatar4.jpg"}, "like_count": 6, "is_liked": false, "reply_count": 0, "total_reply_count": 0, "is_visible": true, "created_at": "2024-01-15T13:30:00Z", "updated_at": "2024-01-15T13:30:00Z"}, {"id": 5, "content": "@张三 同意你的观点，这种分析方法很实用", "comment_type": "article", "article_id": 1, "video_id": null, "level": 1, "parent_id": 1, "reply_to_id": 1, "reply_to_user_id": 101, "reply_to_user": {"id": 101, "username": "张三", "nickname": "技术爱好者", "avatar": "https://example.com/avatar1.jpg"}, "path": "1.5", "author_id": 105, "author": {"id": 105, "username": "孙七", "nickname": "数据分析师", "avatar": "https://example.com/avatar5.jpg"}, "like_count": 4, "is_liked": false, "reply_count": 0, "total_reply_count": 0, "is_visible": true, "created_at": "2024-01-15T14:00:00Z", "updated_at": "2024-01-15T14:00:00Z"}, {"id": 6, "content": "@张三 能否分享一下相关的学习资源？", "comment_type": "article", "article_id": 1, "video_id": null, "level": 1, "parent_id": 1, "reply_to_id": 1, "reply_to_user_id": 101, "reply_to_user": {"id": 101, "username": "张三", "nickname": "技术爱好者", "avatar": "https://example.com/avatar1.jpg"}, "path": "1.6", "author_id": 106, "author": {"id": 106, "username": "周八", "nickname": "学习者", "avatar": "https://example.com/avatar6.jpg"}, "like_count": 2, "is_liked": false, "reply_count": 0, "total_reply_count": 0, "is_visible": true, "created_at": "2024-01-15T15:20:00Z", "updated_at": "2024-01-15T15:20:00Z"}, {"id": 7, "content": "文章内容很有深度，值得收藏！", "comment_type": "article", "article_id": 1, "video_id": null, "level": 0, "parent_id": null, "reply_to_id": null, "reply_to_user_id": null, "reply_to_user": null, "path": "7", "author_id": 107, "author": {"id": 107, "username": "吴九", "nickname": "技术专家", "avatar": "https://example.com/avatar7.jpg"}, "like_count": 18, "is_liked": false, "reply_count": 1, "total_reply_count": 1, "is_visible": true, "created_at": "2024-01-15T16:00:00Z", "updated_at": "2024-01-15T16:00:00Z"}, {"id": 8, "content": "@吴九 我也收藏了，准备仔细研读", "comment_type": "article", "article_id": 1, "video_id": null, "level": 1, "parent_id": 7, "reply_to_id": 7, "reply_to_user_id": 107, "reply_to_user": {"id": 107, "username": "吴九", "nickname": "技术专家", "avatar": "https://example.com/avatar7.jpg"}, "path": "7.8", "author_id": 108, "author": {"id": 108, "username": "郑十", "nickname": "开发工程师", "avatar": "https://example.com/avatar8.jpg"}, "like_count": 3, "is_liked": false, "reply_count": 0, "total_reply_count": 0, "is_visible": true, "created_at": "2024-01-15T16:30:00Z", "updated_at": "2024-01-15T16:30:00Z"}], "has_next": true, "has_previous": false, "next_cursor": "eyJpZCI6OCwiY3JlYXRlZF9hdCI6IjIwMjQtMDEtMTVUMTY6MzA6MDBaIn0=", "previous_cursor": null, "total_count": 15}}, "数据结构说明": {"level": "评论层级，0=顶层评论，1=一级回复，2=二级回复", "path": "评论路径，用点号分隔，如 '1.2.3' 表示评论3是评论2的回复，评论2是评论1的回复", "parent_id": "直接父评论ID", "reply_to_id": "回复目标评论ID（可能不是直接父评论）", "reply_to_user": "回复目标用户信息", "reply_count": "直接回复数量", "total_reply_count": "总回复数量（包括所有子级回复）", "max_level参数": "控制显示的最大层级，0=仅顶层，1=顶层+一级，2=顶层+二级", "排序规则": "先按层级分组，同层级内按指定字段排序（like_count或created_at）"}, "API端点": {"文章评论扁平化": "GET /api/v1/comments/article/{article_id}/flat", "视频评论扁平化": "GET /api/v1/comments/video/{video_id}/flat", "参数说明": {"cursor": "分页游标", "size": "每页数量，最大100", "sort_by": "排序字段，支持 like_count 和 created_at", "max_level": "最大显示层级，0-3之间"}}, "优势": {"性能优化": "避免深层嵌套，减少前端渲染复杂度", "灵活控制": "通过max_level参数控制显示层级", "清晰结构": "通过path字段明确评论层级关系", "完整信息": "包含回复目标用户信息，便于前端显示", "统计准确": "提供直接回复数和总回复数两种统计"}}