# 文章API迁移指南

## 📋 概述

本文档指导如何从旧的文章API端点迁移到新的统一API端点。新的统一API设计提供了更好的一致性、灵活性和可维护性。

## 🎯 迁移目标

- **统一响应格式**：所有API使用一致的响应结构
- **灵活的数据控制**：通过查询参数控制返回的数据内容
- **简化的端点设计**：减少端点数量，提高可维护性
- **更好的错误处理**：统一的错误响应格式

## 🔄 API端点迁移映射

### 1. 创建文章

#### 旧API (已废弃)
```bash
POST /api/v1/articles/unified
```

#### 新API (推荐)
```bash
POST /api/v1/articles/unified
```

**响应格式变化：**
- 旧API：直接返回文章ID
- 新API：返回统一响应格式，包含成功状态和消息

### 2. 获取文章详情

#### 旧API (已废弃)
```bash
# 基础详情
GET /api/v1/articles/{article_id}

# 包含统计信息
GET /api/v1/articles/{article_id}/with-stats
```

#### 新API (推荐)
```bash
# 基础详情
GET /api/v1/articles/unified/{article_id}

# 包含统计信息
GET /api/v1/articles/unified/{article_id}?include_stats=true

# 包含审核信息（需要权限）
GET /api/v1/articles/unified/{article_id}?include_review=true

# 包含元数据
GET /api/v1/articles/unified/{article_id}?include_meta=true

# 组合使用
GET /api/v1/articles/unified/{article_id}?include_stats=true&include_review=true&include_meta=true
```

### 3. 获取文章列表

#### 旧API (已废弃)
```bash
# 基础列表（包含统计信息）
GET /api/v1/articles/unified?include_stats=true&skip=0&limit=10

# 按分类获取（包含统计信息）
GET /api/v1/articles/{category_id}/with-stats?skip=0&limit=10

# 获取用户文章
GET /api/v1/articles/users/{user_id}/articles?status=all&include_stats=true
```

#### 新API (推荐)
```bash
# 基础列表
GET /api/v1/articles/unified?skip=0&limit=10

# 包含统计信息的列表
GET /api/v1/articles/unified?skip=0&limit=10&include_stats=true

# 按分类筛选
GET /api/v1/articles/unified?category_id=1&include_stats=true

# 按作者筛选
GET /api/v1/articles/unified?author_id=1&include_stats=true

# 按状态筛选
GET /api/v1/articles/unified?status=published&include_stats=true
GET /api/v1/articles/unified?status=draft&include_stats=true
GET /api/v1/articles/unified?status=all&include_stats=true

# 组合筛选
GET /api/v1/articles/unified?author_id=1&status=published&include_stats=true&include_review=true
```

## 📊 查询参数说明

### 通用参数
- `skip`: 跳过的记录数（分页）
- `limit`: 返回的最大记录数（分页）

### 筛选参数
- `category_id`: 分类ID筛选
- `author_id`: 作者ID筛选
- `status`: 状态筛选
  - `draft`: 草稿（未发布）
  - `published`: 已发布且审核通过
  - `all`: 所有状态（仅作者本人可用）

### 数据控制参数
- `include_stats`: 是否包含统计信息（点赞数、收藏数、访问量等）
- `include_review`: 是否包含审核信息（仅作者本人或管理员可用）
- `include_meta`: 是否包含元数据（SEO信息等）

## 🔧 响应格式变化

### 旧API响应格式
```json
{
  "id": 1,
  "title": "文章标题",
  "content": "文章内容",
  "author": {...},
  "like_count": 10,
  "favorite_count": 5
}
```

### 新API响应格式
```json
{
  "success": true,
  "message": "获取文章详情成功",
  "data": {
    "id": 1,
    "title": "文章标题",
    "content": "文章内容",
    "author": {...},
    "stats": {
      "like_count": 10,
      "favorite_count": 5,
      "view_count": 100
    },
    "review": {
      "status": "approved",
      "reviewed_at": "2024-01-01T00:00:00Z"
    },
    "meta": {
      "seo_title": "SEO标题",
      "seo_description": "SEO描述"
    }
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 🚀 迁移步骤

### 第一阶段：并行运行（当前状态）
1. ✅ 新的统一API已部署并可用
2. ✅ 旧API仍然可用，但标记为废弃
3. ✅ 在API文档中添加废弃警告

### 第二阶段：客户端迁移
1. 🔄 更新前端应用使用新的统一API
2. 🔄 更新移动应用使用新的统一API
3. 🔄 更新第三方集成使用新的统一API
4. 🔄 更新测试用例

### 第三阶段：清理（计划中）
1. ⏳ 移除旧的API端点
2. ⏳ 清理相关的废弃代码
3. ⏳ 更新文档

## 📝 代码示例

### JavaScript/TypeScript 客户端迁移

#### 旧代码
```javascript
// 获取文章详情
const article = await fetch(`/api/v1/articles/${articleId}`);

// 获取包含统计信息的文章详情
const articleWithStats = await fetch(`/api/v1/articles/${articleId}/with-stats`);

// 获取文章列表
const articles = await fetch('/api/v1/articles/unified?include_stats=true&skip=0&limit=10');
```

#### 新代码
```javascript
// 获取文章详情
const response = await fetch(`/api/v1/articles/unified/${articleId}`);
const result = await response.json();
if (result.success) {
  const article = result.data;
}

// 获取包含统计信息的文章详情
const response = await fetch(`/api/v1/articles/unified/${articleId}?include_stats=true`);
const result = await response.json();
if (result.success) {
  const articleWithStats = result.data;
}

// 获取文章列表
const response = await fetch('/api/v1/articles/unified?skip=0&limit=10&include_stats=true');
const result = await response.json();
if (result.success) {
  const articles = result.data.items;
  const total = result.data.total;
}
```

### Python 客户端迁移

#### 旧代码
```python
import requests

# 获取文章详情
response = requests.get(f'/api/v1/articles/{article_id}')
article = response.json()

# 获取包含统计信息的文章详情
response = requests.get(f'/api/v1/articles/{article_id}/with-stats')
article_with_stats = response.json()
```

#### 新代码
```python
import requests

# 获取文章详情
response = requests.get(f'/api/v1/articles/unified/{article_id}')
result = response.json()
if result['success']:
    article = result['data']

# 获取包含统计信息的文章详情
response = requests.get(f'/api/v1/articles/unified/{article_id}?include_stats=true')
result = response.json()
if result['success']:
    article_with_stats = result['data']
```

## ⚠️ 注意事项

1. **权限控制**：新API保持与旧API相同的权限控制逻辑
2. **数据格式**：新API的数据结构可能略有不同，请仔细检查字段映射
3. **错误处理**：新API使用统一的错误响应格式，需要更新错误处理逻辑
4. **性能**：新API通过按需加载数据可能提供更好的性能

## 🆘 支持与帮助

如果在迁移过程中遇到问题，请：

1. 查看本文档的详细说明
2. 检查API响应中的错误信息
3. 联系开发团队获取支持

## 📅 时间线

- **2024-01-01**: 新统一API发布
- **2024-01-15**: 开始客户端迁移
- **2024-02-01**: 完成主要客户端迁移
- **2024-02-15**: 移除旧API端点（计划）

---

**注意**：本迁移指南会根据实际情况持续更新，请定期查看最新版本。