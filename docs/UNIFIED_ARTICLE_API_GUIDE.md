# 统一文章列表API使用指南

## 🎯 概述

本文档介绍了新的统一文章列表API，它整合了原有的 `/my` 和 `/my/with-review` 两个端点的功能，提供了更灵活和强大的文章列表获取能力。

## 🔗 API端点

```
GET /api/v1/articles/users/{user_id}/articles
```

## 🔐 权限控制

### 访问规则

1. **作者本人访问**：
   - 可以访问所有状态的文章（草稿、已发布、待审核、被拒绝等）
   - 可以使用 `status` 参数筛选文章状态
   - 可以获取审核信息（当 `include_review=true` 时）

2. **其他用户访问**：
   - 只能访问已发布且已审核通过的文章
   - `status` 参数被忽略，强制为 `published_approved`
   - 不能获取审核信息（即使设置 `include_review=true`）

3. **游客访问**：
   - 与其他用户访问规则相同
   - 只能访问已发布且已审核通过的文章

## 📋 请求参数

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| `user_id` | int | ✅ | - | 目标用户ID（路径参数） |
| `skip` | int | ❌ | 0 | 跳过的记录数（分页） |
| `limit` | int | ❌ | 100 | 返回的最大记录数 |
| `status` | string | ❌ | "all" | 文章状态筛选（仅作者本人可用） |
| `include_stats` | bool | ❌ | true | 是否包含统计信息 |
| `include_review` | bool | ❌ | false | 是否包含审核信息（仅作者本人可用） |

### 状态筛选选项

- `all`: 所有文章
- `draft`: 草稿（未发布）
- `published_approved`: 已发布且已审核通过
- `published_pending`: 已发布但待审核
- `published_rejected`: 已发布但审核被拒绝

## 📤 响应格式

```json
{
  "total": 10,
  "items": [
    {
      "id": 1,
      "title": "文章标题",
      "description": "文章描述",
      "cover_url": "封面图URL",
      "author_id": 1,
      "is_published": true,
      "is_approved": true,
      "visit_count": 100,
      "created_at": "2024-01-01T00:00:00",
      "updated_at": "2024-01-01T00:00:00",
      "category_id": 1,
      "tags": ["标签1", "标签2"],
      "author": {
        "id": 1,
        "username": "作者用户名",
        "nickname": "作者昵称"
      },
      "like_count": 10,
      "favorite_count": 5,
      "is_liked_by_user": false,
      "is_favorited_by_user": false,
      "review": {
        "id": 1,
        "status": "approved",
        "comment": "审核通过",
        "reviewer_id": 2,
        "created_at": "2024-01-01T00:00:00"
      }
    }
  ]
}
```

## 🚀 使用示例

### 1. 作者本人获取所有文章（包含统计和审核信息）

```bash
curl -X GET "http://localhost:8000/api/v1/articles/users/1/articles?include_stats=true&include_review=true" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. 作者本人获取草稿文章

```bash
curl -X GET "http://localhost:8000/api/v1/articles/users/1/articles?status=draft&include_stats=true" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 其他用户访问某用户的文章

```bash
curl -X GET "http://localhost:8000/api/v1/articles/users/1/articles?include_stats=true" \
  -H "Authorization: Bearer OTHER_USER_TOKEN"
```

### 4. 游客访问文章列表

```bash
curl -X GET "http://localhost:8000/api/v1/articles/users/1/articles?include_stats=true"
```

### 5. 分页获取文章

```bash
curl -X GET "http://localhost:8000/api/v1/articles/users/1/articles?skip=20&limit=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🔄 迁移指南

### 从旧API迁移

#### 原 `/my` 端点
```bash
# 旧的调用方式
GET /api/v1/articles/my?status=all&skip=0&limit=100

# 新的调用方式
GET /api/v1/articles/users/{current_user_id}/articles?status=all&skip=0&limit=100&include_stats=true&include_review=false
```

#### 原 `/my/with-review` 端点
```bash
# 旧的调用方式
GET /api/v1/articles/my/with-review?status=all&skip=0&limit=100

# 新的调用方式
GET /api/v1/articles/users/{current_user_id}/articles?status=all&skip=0&limit=100&include_stats=false&include_review=true
```

## ⚡ 性能优化

1. **批量统计信息获取**：使用缓存服务批量获取点赞、收藏、访问量等统计信息
2. **权限检查优化**：在CRUD层面进行权限过滤，减少不必要的数据查询
3. **可选字段加载**：根据参数按需加载统计信息和审核信息

## 🛡️ 安全考虑

1. **权限隔离**：严格控制不同用户的访问权限
2. **数据脱敏**：其他用户无法获取敏感的审核信息
3. **参数验证**：对所有输入参数进行验证和清理

## 🔧 技术实现

### 核心组件

1. **CRUD层**：`app/crud/article.py` - `get_user_articles_with_permission` 方法
2. **服务层**：`app/services/unified_article_service.py` - 统一文章服务
3. **API层**：`app/api/endpoints/articles.py` - API端点实现

### 权限检查逻辑

```python
# 权限检查：判断是否为作者本人
is_author = current_user and current_user.id == user_id

# 如果不是作者本人，只能访问已发布且已审核通过的文章
if not is_author:
    status = ArticleStatus.PUBLISHED_APPROVED
    include_review = False  # 其他用户不能看到审核信息
```

## 📝 注意事项

1. **向后兼容**：旧的API端点仍然可用，但建议迁移到新的统一API
2. **权限控制**：确保正确传递用户认证信息
3. **参数组合**：合理使用 `include_stats` 和 `include_review` 参数以优化性能
4. **错误处理**：注意处理用户不存在、权限不足等错误情况

## 🎉 总结

新的统一文章列表API提供了：

- ✅ 统一的接口设计
- ✅ 灵活的权限控制
- ✅ 可选的数据加载
- ✅ 良好的性能优化
- ✅ 完整的向后兼容

这个API设计既满足了当前的需求，也为未来的扩展提供了良好的基础。
