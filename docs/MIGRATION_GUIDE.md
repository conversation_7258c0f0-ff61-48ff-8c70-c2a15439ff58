# 文章API迁移指南

## 🎯 迁移概述

本指南帮助你从旧的多个文章API端点迁移到新的统一API设计。新设计提供了更简洁、更灵活的接口。

## 📊 迁移对比表

### API端点迁移

| 旧端点 | 新端点 | 说明 |
|--------|--------|------|
| `GET /articles/{id}` | `GET /articles/unified/{id}` | 基础文章详情 |
| `GET /articles/{id}/with-stats` | `GET /articles/unified/{id}?include_stats=true` | 包含统计信息的详情 |
| `GET /articles/with-stats` | `GET /articles/unified?include_stats=true` | 包含统计信息的列表 |
| `GET /articles/{category_id}/with-stats` | `GET /articles/unified?category_id={id}&include_stats=true` | 分类文章列表 |
| `GET /articles/users/{user_id}/articles` | `GET /articles/unified?author_id={id}&include_stats=true&include_review=true` | 用户文章列表 |

### 响应模型迁移

| 旧模型 | 新模型 | 迁移方式 |
|--------|--------|----------|
| `ArticleDetail` | `UnifiedContentResponse` | 直接替换 |
| `ArticleWithStats` | `UnifiedContentResponse` | 使用 `include_stats=true` |
| `ArticleListWithStats` | `PaginatedResponse[UnifiedContentResponse]` | 使用 `include_stats=true` |
| `ArticleWithStatsAndReview` | `UnifiedContentResponse` | 使用 `include_stats=true&include_review=true` |

## 🔧 具体迁移步骤

### 步骤1: 更新API调用

#### 旧方式
```javascript
// 获取文章详情
const response = await fetch('/api/articles/123');

// 获取包含统计信息的文章详情
const statsResponse = await fetch('/api/articles/123/with-stats');

// 获取用户文章列表
const userArticles = await fetch('/api/articles/users/456/articles?include_stats=true');
```

#### 新方式
```javascript
// 获取文章详情
const response = await fetch('/api/articles/unified/123');

// 获取包含统计信息的文章详情
const statsResponse = await fetch('/api/articles/unified/123?include_stats=true');

// 获取用户文章列表
const userArticles = await fetch('/api/articles/unified?author_id=456&include_stats=true&include_review=true');
```

### 步骤2: 更新响应处理

#### 旧响应格式
```json
{
  "total": 10,
  "items": [
    {
      "id": 1,
      "title": "文章标题",
      "like_count": 10,
      "favorite_count": 5
    }
  ]
}
```

#### 新响应格式
```json
{
  "status": "success",
  "message": "获取文章列表成功",
  "data": {
    "total": 10,
    "items": [
      {
        "id": 1,
        "title": "文章标题",
        "stats": {
          "like_count": 10,
          "favorite_count": 5,
          "visit_count": 100,
          "is_liked_by_user": true,
          "is_favorited_by_user": false
        }
      }
    ],
    "page": 1,
    "page_size": 10,
    "has_next": false,
    "has_prev": false
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 步骤3: 更新错误处理

#### 新的错误响应格式
```json
{
  "status": "error",
  "message": "文章不存在",
  "data": null,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 🚀 新功能优势

### 1. 灵活的数据控制
```javascript
// 只获取基础信息
fetch('/api/articles/unified/123');

// 获取包含统计信息
fetch('/api/articles/unified/123?include_stats=true');

// 获取包含审核信息（需要权限）
fetch('/api/articles/unified/123?include_review=true');

// 获取包含元数据
fetch('/api/articles/unified/123?include_meta=true');

// 组合使用
fetch('/api/articles/unified/123?include_stats=true&include_review=true&include_meta=true');
```

### 2. 统一的筛选参数
```javascript
// 按分类筛选
fetch('/api/articles/unified?category_id=1');

// 按作者筛选
fetch('/api/articles/unified?author_id=2');

// 按状态筛选
fetch('/api/articles/unified?status=published');

// 组合筛选
fetch('/api/articles/unified?category_id=1&author_id=2&status=published&include_stats=true');
```

### 3. 改进的分页信息
新的响应包含更详细的分页信息：
- `page`: 当前页码
- `page_size`: 每页大小
- `has_next`: 是否有下一页
- `has_prev`: 是否有上一页

## ⚠️ 注意事项

### 1. 向后兼容性
- 旧的API端点仍然可用，但建议尽快迁移
- 旧的响应模型仍然支持，但可能在未来版本中被移除

### 2. 权限变化
- 新API支持跨用户访问，但有严格的权限控制
- `include_review=true` 只对文章作者和管理员有效

### 3. 性能优化
- 新API支持按需加载数据，避免不必要的查询
- 建议根据实际需求选择合适的参数组合

## 📝 迁移检查清单

- [ ] 更新所有API调用URL
- [ ] 更新响应数据处理逻辑
- [ ] 更新错误处理逻辑
- [ ] 测试新的权限控制
- [ ] 验证分页功能
- [ ] 更新文档和注释
- [ ] 通知团队成员API变更

## 🔗 相关文档

- [统一API响应设计](./UNIFIED_API_RESPONSE_DESIGN.md)
- [API使用示例](./API_EXAMPLES.md)
- [权限系统说明](./PERMISSION_SYSTEM.md)

## 🆘 获取帮助

如果在迁移过程中遇到问题，请：
1. 查看相关文档
2. 检查API响应格式
3. 验证权限设置
4. 联系开发团队

---

**迁移建议**: 建议在测试环境中先完成迁移和验证，确认无误后再部署到生产环境。
