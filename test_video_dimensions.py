#!/usr/bin/env python3
"""
测试视频尺寸字段功能的简单脚本
"""

import asyncio

from sqlalchemy import select

from app.db.session import SessionLocal
from app.models.user import User
from app.models.video import Video


async def test_video_dimensions():
    """测试视频尺寸字段"""

    async with SessionLocal() as db:
        # 1. 查找一个测试用户
        result = await db.execute(select(User).limit(1))
        user = result.scalar_one_or_none()

        if not user:
            print("❌ 没有找到测试用户")
            return

        print(f"✅ 找到测试用户: {user.username} (ID: {user.id})")

        # 2. 查找该用户的一个视频
        result = await db.execute(select(Video).where(Video.author_id == user.id).limit(1))
        video = result.scalar_one_or_none()

        if not video:
            print("❌ 该用户没有视频")
            return

        print(f"✅ 找到测试视频: {video.title} (ID: {video.id})")
        print(f"   当前尺寸: {video.width}x{video.height}, 时长: {video.duration}秒")

        # 3. 测试更新视频尺寸
        print("\n🔄 测试更新视频尺寸...")

        # 更新视频尺寸
        video.width = 1920
        video.height = 1080
        video.duration = 300

        await db.commit()
        await db.refresh(video)

        print("✅ 更新成功!")
        print(f"   新尺寸: {video.width}x{video.height}, 时长: {video.duration}秒")

        # 4. 验证数据库中的数据
        result = await db.execute(select(Video).where(Video.id == video.id))
        updated_video = result.scalar_one()

        assert updated_video.width == 1920
        assert updated_video.height == 1080
        assert updated_video.duration == 300

        print("✅ 数据库验证通过!")

        # 5. 测试设置为 None
        print("\n🔄 测试设置为 None...")

        video.width = None
        video.height = None
        video.duration = None

        await db.commit()
        await db.refresh(video)

        print("✅ 设置为 None 成功!")
        print(f"   尺寸: {video.width}x{video.height}, 时长: {video.duration}")

        assert updated_video.width is None
        assert updated_video.height is None
        assert updated_video.duration is None

        print("✅ None 值验证通过!")


async def main():
    """主函数"""
    print("🚀 开始测试视频尺寸字段功能...\n")

    try:
        await test_video_dimensions()
        print("\n🎉 所有测试通过!")

    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
