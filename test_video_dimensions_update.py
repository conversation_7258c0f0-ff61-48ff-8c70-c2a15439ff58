"""测试视频尺寸字段更新功能"""

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.tests.utils.utils import random_lower_string


class TestVideoDimensionsUpdate:
    """测试视频尺寸字段更新"""

    async def test_update_video_dimensions(
        self, client: AsyncClient, superuser_token_headers: dict, db: AsyncSession
    ):
        """测试更新视频的宽度和高度"""
        # 1. 创建测试视频
        video_data = {
            "title": "测试视频",
            "description": "测试视频描述",
            "url": "https://example.com/test-video.mp4",
            "cover_url": "https://example.com/cover.jpg",
            "duration": 120,
            "width": 1920,
            "height": 1080,
        }
        
        response = await client.post(
            f"{settings.API_V1_STR}/videos/",
            headers=superuser_token_headers,
            json=video_data,
        )
        assert response.status_code == 200
        created_video = response.json()
        video_id = created_video["id"]
        
        # 验证创建时的尺寸
        assert created_video["width"] == 1920
        assert created_video["height"] == 1080
        assert created_video["duration"] == 120

        # 2. 更新视频尺寸
        update_data = {
            "width": 3840,
            "height": 2160,
            "duration": 180,
        }
        
        response = await client.put(
            f"{settings.API_V1_STR}/videos/{video_id}",
            headers=superuser_token_headers,
            json=update_data,
        )
        assert response.status_code == 200
        updated_video = response.json()
        
        # 验证更新后的尺寸
        assert updated_video["width"] == 3840
        assert updated_video["height"] == 2160
        assert updated_video["duration"] == 180
        
        # 验证其他字段未被修改
        assert updated_video["title"] == video_data["title"]
        assert updated_video["description"] == video_data["description"]

    async def test_update_video_partial_dimensions(
        self, client: AsyncClient, superuser_token_headers: dict, db: AsyncSession
    ):
        """测试部分更新视频尺寸"""
        # 1. 创建测试视频
        video_data = {
            "title": "测试视频2",
            "description": "测试视频描述2",
            "url": "https://example.com/test-video2.mp4",
            "width": 1280,
            "height": 720,
        }
        
        response = await client.post(
            f"{settings.API_V1_STR}/videos/",
            headers=superuser_token_headers,
            json=video_data,
        )
        assert response.status_code == 200
        created_video = response.json()
        video_id = created_video["id"]

        # 2. 只更新宽度
        update_data = {"width": 1920}
        
        response = await client.put(
            f"{settings.API_V1_STR}/videos/{video_id}",
            headers=superuser_token_headers,
            json=update_data,
        )
        assert response.status_code == 200
        updated_video = response.json()
        
        # 验证只有宽度被更新
        assert updated_video["width"] == 1920
        assert updated_video["height"] == 720  # 保持原值

        # 3. 只更新高度
        update_data = {"height": 1080}
        
        response = await client.put(
            f"{settings.API_V1_STR}/videos/{video_id}",
            headers=superuser_token_headers,
            json=update_data,
        )
        assert response.status_code == 200
        updated_video = response.json()
        
        # 验证只有高度被更新
        assert updated_video["width"] == 1920  # 保持上次更新的值
        assert updated_video["height"] == 1080

    async def test_update_video_dimensions_with_content(
        self, client: AsyncClient, superuser_token_headers: dict, db: AsyncSession
    ):
        """测试同时更新视频尺寸和内容"""
        # 1. 创建测试视频
        video_data = {
            "title": "测试视频3",
            "url": "https://example.com/test-video3.mp4",
            "width": 640,
            "height": 480,
        }
        
        response = await client.post(
            f"{settings.API_V1_STR}/videos/",
            headers=superuser_token_headers,
            json=video_data,
        )
        assert response.status_code == 200
        created_video = response.json()
        video_id = created_video["id"]

        # 2. 同时更新内容和尺寸
        update_data = {
            "title": "更新后的标题",
            "description": "更新后的描述",
            "width": 1920,
            "height": 1080,
            "duration": 300,
        }
        
        response = await client.put(
            f"{settings.API_V1_STR}/videos/{video_id}",
            headers=superuser_token_headers,
            json=update_data,
        )
        assert response.status_code == 200
        updated_video = response.json()
        
        # 验证所有字段都被正确更新
        assert updated_video["title"] == "更新后的标题"
        assert updated_video["description"] == "更新后的描述"
        assert updated_video["width"] == 1920
        assert updated_video["height"] == 1080
        assert updated_video["duration"] == 300

    async def test_update_video_dimensions_null_values(
        self, client: AsyncClient, superuser_token_headers: dict, db: AsyncSession
    ):
        """测试将视频尺寸设置为空值"""
        # 1. 创建有尺寸的测试视频
        video_data = {
            "title": "测试视频4",
            "url": "https://example.com/test-video4.mp4",
            "width": 1920,
            "height": 1080,
            "duration": 120,
        }
        
        response = await client.post(
            f"{settings.API_V1_STR}/videos/",
            headers=superuser_token_headers,
            json=video_data,
        )
        assert response.status_code == 200
        created_video = response.json()
        video_id = created_video["id"]

        # 2. 将尺寸设置为 null
        update_data = {
            "width": None,
            "height": None,
            "duration": None,
        }
        
        response = await client.put(
            f"{settings.API_V1_STR}/videos/{video_id}",
            headers=superuser_token_headers,
            json=update_data,
        )
        assert response.status_code == 200
        updated_video = response.json()
        
        # 验证尺寸被设置为 null
        assert updated_video["width"] is None
        assert updated_video["height"] is None
        assert updated_video["duration"] is None
