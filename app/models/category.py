from datetime import datetime

from sqlalchemy import (
    Column,
    DateTime,
    Foreign<PERSON>ey,
    Integer,
    String,
)
from sqlalchemy.orm import relationship

from app.db.session import Base


class Category(Base):
    """
    类别数据模型 - 优化版
    使用 SQLAlchemy 的自引用关系来高效处理层级关系。
    """

    __tablename__ = "categories"

    id = Column(Integer, primary_key=True, index=True)
    parent_id = Column(Integer, ForeignKey("categories.id"), nullable=True, index=True)
    children = relationship("Category", back_populates="parent", cascade="all, delete-orphan")
    parent = relationship("Category", back_populates="children", remote_side=[id])
    name = Column(String(50), unique=True, index=True, nullable=False)
    description = Column(String(255), nullable=True)
    category_type = Column(
        String(50), index=True, nullable=False, comment="类别类型: article 或 video"
    )

    # 统计缓存字段
    article_count = Column(Integer, default=0, nullable=False, comment="文章数量")
    video_count = Column(Integer, default=0, nullable=False, comment="视频数量")

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关联关系
    articles = relationship("Article", back_populates="category")
    videos = relationship("Video", back_populates="category")

    def __repr__(self):
        return f"<Category {self.name}>"
