import json
from typing import Any

from fastapi import HTTPException, Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from app.services.logger import logger


class ExceptionHandlerMiddleware(BaseHTTPMiddleware):
    """统一异常处理中间件"""

    async def dispatch(self, request: Request, call_next) -> Any:
        try:
            response = await call_next(request)
            return response
        except HTTPException as http_exc:
            # 记录客户端错误（4xx）的详细信息，用于调试
            if 400 <= http_exc.status_code < 500:
                logger.info(
                    f"客户端错误 {http_exc.status_code}: {http_exc.detail} "
                    f"- {request.method} {request.url.path}"
                )
            # 返回统一格式的错误响应
            return JSONResponse(
                status_code=http_exc.status_code,
                content={
                    "status": "error",
                    "message": http_exc.detail,
                    "status_code": http_exc.status_code,
                },
            )
        except Exception as e:
            # 记录真正的服务器错误
            logger.error(
                f"未处理的异常: {str(e)} - {request.method} {request.url.path}", exc_info=True
            )
            # 返回统一格式的500错误响应
            return JSONResponse(
                status_code=500,
                content={
                    "status": "error",
                    "message": "服务器内部错误",
                    "detail": "请联系管理员或稍后重试",
                },
            )


class ResponseFormatterMiddleware(BaseHTTPMiddleware):
    """统一响应格式化中间件 - 避免双重包装"""

    def _is_base_response_format(self, content: dict) -> bool:
        """检测是否已经是BaseResponse格式或其他已格式化的响应"""
        if not isinstance(content, dict):
            return False

        content_fields = set(content.keys())

        # 检查是否已经是标准的成功响应格式
        if "status" in content_fields and content.get("status") == "success":
            return True

        # 检查是否已经是标准的错误响应格式
        if "status" in content_fields and content.get("status") == "error":
            return True

        # 检查是否包含BaseResponse的关键字段组合
        if "success" in content_fields and "status" in content_fields:
            return True

        # 检查是否包含完整的BaseResponse字段
        base_response_fields = {"success", "status", "message", "data", "timestamp"}
        return len(content_fields.intersection(base_response_fields)) >= 3

    async def dispatch(self, request: Request, call_next) -> Any:
        response = await call_next(request)

        # 对于OPTIONS请求（CORS预检）和204状态码，直接返回原始响应
        if request.method == "OPTIONS" or response.status_code == 204:
            return response

        if (
            request.url.path.startswith("/api/v1")
            and response.status_code < 400
            and response.status_code != 204
            and request.url.path not in ["/api/v1/health"]
        ):
            try:
                body = b""
                async for chunk in response.body_iterator:
                    body += chunk
                content = json.loads(body.decode("utf-8")) if body else {}

                # 检查是否已经是BaseResponse格式，避免双重包装
                if self._is_base_response_format(content):
                    # 已经是BaseResponse格式，直接返回
                    formatted_content = content
                else:
                    # 普通数据，进行包装
                    formatted_content = {"status": "success", "data": content}

                response_body = json.dumps(
                    formatted_content, ensure_ascii=False, default=str
                ).encode("utf-8")

                # 创建新的headers字典，排除Content-Length让FastAPI自动计算
                new_headers = {
                    k: v for k, v in response.headers.items() if k.lower() != "content-length"
                }
                return Response(
                    content=response_body,
                    status_code=response.status_code,
                    headers=new_headers,
                    media_type=response.media_type or "application/json",
                )
            except Exception:
                # 异常情况下也要排除Content-Length头部
                new_headers = {
                    k: v for k, v in response.headers.items() if k.lower() != "content-length"
                }
                return Response(
                    content="Internal Server Error",
                    status_code=500,
                    headers=new_headers,
                    media_type=response.media_type,
                )
        return response
