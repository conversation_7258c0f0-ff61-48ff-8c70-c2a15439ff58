from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Path, Query, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.api import deps
from app.api.permission_deps import PermissionDeps
from app.core.pagination import CursorPaginationParams, CursorPaginationResponse
from app.core.permission_system import (
    Action,
    ResourceType,
    Scope,
)
from app.core.permission_system import (
    Permission as PermissionObject,
)
from app.schemas.unified_response import ContentResponseOptions, UnifiedContentResponse
from app.schemas.video import VideoStatus
from app.services.content_service import video_service
from app.services.content_stats_service import ContentStatsService
from app.services.history_service import record_user_history
from app.services.logger import get_logger
from app.services.recommendation_service import recommendation_service
from app.services.unified_response_service import UnifiedResponseService
from app.services.video_folder_service import VideoFolderService
from app.services.video_response_service import video_response_service

# 初始化服务
content_stats_service = ContentStatsService()

logger = get_logger(__name__)

router = APIRouter()


@router.post("/", response_model=schemas.Video)
async def create_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_in: schemas.VideoCreate,
    current_user: models.User = Depends(
        PermissionDeps.require_permission_enhanced(
            PermissionObject(resource=ResourceType.VIDEO, action=Action.CREATE, scope=Scope.OWN)
        )
    ),
) -> Any:
    """创建新视频
    - 未发布的视频（草稿）不需要审核
    - 发布的视频需要审核
    - 如果未指定folder_id，则使用用户的默认文件夹
    """
    # 处理文件夹ID
    if video_in.folder_id is None:
        # 获取用户的默认文件夹
        default_folder = await VideoFolderService.create_default_folder(db, current_user.id)
        video_in_dict = video_in.dict()
        video_in_dict["folder_id"] = default_folder.id
        video = await crud.video.create(db=db, obj_in=video_in_dict)
    else:
        # 验证文件夹是否存在且属于当前用户
        result = await db.execute(
            select(models.VideoFolder).where(
                models.VideoFolder.id == video_in.folder_id,
                models.VideoFolder.user_id == current_user.id,
                not models.VideoFolder.is_deleted,
            )
        )
        folder = result.scalar_one_or_none()
        if not folder:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="指定的文件夹不存在或不属于当前用户"
            )
        video = await crud.video.create(db=db, obj_in=video_in)

    # 处理发布状态
    video = await video_service.handle_publish_status(
        db=db, content=video, is_published=video.is_published
    )
    return video


@router.get("/", response_model=CursorPaginationResponse[UnifiedContentResponse])
async def read_videos(
    *,
    db: AsyncSession = Depends(deps.get_db),
    pagination: CursorPaginationParams = Depends(),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    # 筛选参数
    status: VideoStatus | None = Query(None, description="视频状态筛选"),
    category_id: int | None = Query(None, description="分类ID"),
    author_id: int | None = Query(None, description="作者ID"),
    folder_id: int | None = Query(None, description="文件夹ID"),
    # 响应选项
    include_author: bool = Query(True, description="是否包含作者信息"),
    include_category: bool = Query(True, description="是否包含分类信息"),
    include_tags: bool = Query(True, description="是否包含标签信息"),
    include_stats: bool = Query(True, description="是否包含统计信息"),
    include_review: bool = Query(False, description="是否包含审核信息"),
    include_meta: bool = Query(True, description="是否包含元数据"),
    include_total: bool = Query(False, description="是否包含总数（影响性能）"),
) -> Any:
    """获取视频列表（重构版）
    - 支持游标分页
    - 支持多种筛选条件
    - 统一响应格式
    - 权限控制
    """
    # 构建筛选条件
    filters = {}
    if status:
        filters["status"] = status.value
    if category_id:
        filters["category_id"] = category_id
    if author_id:
        filters["author_id"] = author_id
    if folder_id:
        filters["folder_id"] = folder_id

    # 获取分页数据
    paginated_result = await crud.video.get_paginated_videos(
        db=db,
        params=pagination,
        filters=filters,
        current_user=current_user,
        include_review=include_review,
        include_total=include_total,
    )

    # 构建响应选项
    options = ContentResponseOptions(
        include_author=include_author,
        include_category=include_category,
        include_tags=include_tags,
        include_stats=include_stats,
        include_review=include_review,
        include_meta=include_meta,
    )

    # 构建统一响应
    current_user_id = current_user.id if current_user else None
    response_items = await video_response_service.build_video_list_response(
        db=db,
        videos=paginated_result.items,
        options=options,
        current_user_id=current_user_id,
    )

    # 返回分页响应
    return CursorPaginationResponse(
        items=response_items,
        has_next=paginated_result.has_next,
        has_previous=paginated_result.has_previous,
        next_cursor=paginated_result.next_cursor,
        previous_cursor=paginated_result.previous_cursor,
        total_count=paginated_result.total_count,
    )


@router.get(
    "/users/{user_id}/folders",
    response_model=list[schemas.VideoFolder],
    summary="获取指定用户的文件夹列表（智能加载）",
)
async def get_user_folders(
    user_id: int,
    *,
    db: AsyncSession = Depends(deps.get_db),
    include_video_count: bool = Query(True, description="是否包含视频数量统计"),
    include_children: bool = Query(False, description="是否包含子文件夹信息"),
    include_recent_videos: bool = Query(
        False, description="是否包含每个文件夹的最新视频预览（最多3个）"
    ),
    path: str = Query("/", description="指定路径下的文件夹，默认为根路径"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
) -> Any:
    """
    获取指定用户的文件夹列表（智能加载策略）

    **权限控制**：
    - **用户本人**：可以访问所有文件夹（包括私有文件夹）
    - **其他用户**：只能访问公开文件夹
    - **未登录用户**：只能访问公开文件夹

    **智能加载策略**：
    - **基础模式**：只返回文件夹结构和视频数量统计
    - **预览模式**：`include_recent_videos=true` 时，每个文件夹包含最新3个视频的预览
    - **完整模式**：用户点击文件夹后，通过 `/folders/{folder_id}/videos` 获取完整视频列表

    **功能特性**：
    - 支持按路径获取文件夹
    - 可选择是否包含视频数量统计
    - 可选择是否包含子文件夹信息
    - 可选择是否包含最新视频预览
    - 自动权限过滤
    """
    # 1. 检查目标用户是否存在
    target_user = await crud.user.get(db=db, id=user_id)
    if not target_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")

    # 2. 权限判断
    is_own_folders = current_user and current_user.id == user_id

    # 权限检查：如果是访问他人的文件夹，需要有相应权限或只能看公开的
    if not is_own_folders:
        from app.core.permission_system import Permission, PermissionChecker

        # 检查是否有查看所有文件夹的权限
        has_view_all_permission = await PermissionChecker.check_permission(
            db,
            current_user,
            Permission(resource=ResourceType.FOLDER, action=Action.READ, scope=Scope.ALL),
        )
        # 如果没有管理权限，只能查看公开文件夹（后续会过滤）
        if not has_view_all_permission:
            user_info = current_user.id if current_user else "anonymous"
            logger.info(f"用户 {user_info} 访问用户 {user_id} 的公开文件夹")

    # 3. 获取用户的文件夹列表
    folders = await crud.video_folder.get_children(db, user_id=user_id, parent_path=path)

    # 4. 权限过滤：非本人只能看到公开文件夹
    if not is_own_folders:
        folders = [folder for folder in folders if folder.is_public]

    # 5. 补充文件夹信息
    for folder in folders:
        if include_video_count:
            # 获取文件夹中的视频数量
            from sqlalchemy import func

            folder.video_count = (
                await db.execute(
                    select(func.count())
                    .select_from(crud.video.model)
                    .where(
                        crud.video.model.folder_id == folder.id,
                        crud.video.model.is_deleted.is_(False),
                    )
                )
            ).scalar()

        if include_children:
            # 检查是否有子文件夹
            children = await crud.video_folder.get_children(
                db, user_id=user_id, parent_path=folder.path
            )
            # 如果不是本人，也要过滤子文件夹的公开性
            if not is_own_folders:
                children = [child for child in children if child.is_public]
            folder.has_children = bool(children)

        if include_recent_videos:
            # 获取文件夹中最新的3个视频作为预览
            video_query = select(crud.video.model).where(
                crud.video.model.folder_id == folder.id,
                crud.video.model.is_deleted.is_(False),
            )

            # 根据权限过滤视频
            if not is_own_folders:
                video_query = video_query.where(
                    crud.video.model.is_published.is_(True),
                    crud.video.model.is_approved.is_(True),
                )

            video_query = video_query.order_by(crud.video.model.created_at.desc()).limit(3)

            result = await db.execute(video_query)
            recent_videos = result.scalars().all()

            # 将视频信息添加到文件夹对象（需要扩展 schema）
            folder.recent_videos = [
                {
                    "id": video.id,
                    "title": video.title,
                    "cover_url": video.cover_url,
                    "duration": video.duration,
                    "created_at": video.created_at,
                }
                for video in recent_videos
            ]

    return folders


@router.get(
    "/folders/{folder_id}/videos",
    response_model=CursorPaginationResponse[UnifiedContentResponse],
    summary="获取指定文件夹下的视频列表",
)
async def get_folder_videos(
    folder_id: int,
    *,
    db: AsyncSession = Depends(deps.get_db),
    pagination: CursorPaginationParams = Depends(),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    # 响应选项
    include_author: bool = Query(True, description="是否包含作者信息"),
    include_category: bool = Query(True, description="是否包含分类信息"),
    include_tags: bool = Query(True, description="是否包含标签信息"),
    include_stats: bool = Query(True, description="是否包含统计信息"),
    include_review: bool = Query(False, description="是否包含审核信息"),
    include_meta: bool = Query(True, description="是否包含元数据"),
    include_total: bool = Query(False, description="是否包含总数（影响性能）"),
) -> Any:
    """
    获取指定文件夹下的视频列表

    **权限控制**：
    - **文件夹所有者**：可以访问所有状态的视频
    - **其他用户**：只能访问已发布且已审核通过的视频
    - **未登录用户**：只能访问公开文件夹中已发布且已审核通过的视频

    **功能特性**：
    - 支持游标分页
    - 可选择包含的信息
    - 自动权限过滤
    - 统一响应格式
    """
    # 1. 检查文件夹是否存在
    folder = await crud.video_folder.get(db=db, id=folder_id)
    if not folder:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="文件夹不存在")

    # 2. 权限检查
    is_own_folder = current_user and current_user.id == folder.user_id

    # 如果不是文件夹所有者，检查文件夹是否公开
    if not is_own_folder and not folder.is_public:
        # 检查是否有查看所有文件夹的权限
        from app.core.permission_system import Permission, PermissionChecker

        has_view_all_permission = await PermissionChecker.check_permission(
            db,
            current_user,
            Permission(resource=ResourceType.FOLDER, action=Action.READ, scope=Scope.ALL),
        )
        if not has_view_all_permission:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="无权访问此文件夹")

    # 3. 构建筛选条件
    filters = {"folder_id": folder_id}

    # 根据权限设置状态筛选
    if is_own_folder:
        # 文件夹所有者可以看到所有视频
        pass
    else:
        # 其他用户只能看到已发布且已审核的视频
        filters["status"] = "published_approved"

    # 4. 获取分页数据
    paginated_result = await crud.video.get_paginated_videos(
        db=db,
        params=pagination,
        filters=filters,
        current_user=current_user,
        include_review=include_review and is_own_folder,  # 只有所有者能看审核信息
        include_total=include_total,
    )

    # 5. 构建响应选项
    options = ContentResponseOptions(
        include_author=include_author,
        include_category=include_category,
        include_tags=include_tags,
        include_stats=include_stats,
        include_review=include_review and is_own_folder,
        include_meta=include_meta,
    )

    # 6. 构建响应项列表
    response_items = await UnifiedResponseService.build_content_list_response(
        db=db,
        contents=paginated_result.items,
        options=options,
        total=paginated_result.total_count,
    )

    # 7. 构建并返回最终的分页响应
    return CursorPaginationResponse(
        items=response_items,
        has_next=paginated_result.has_next,
        has_previous=paginated_result.has_previous,
        next_cursor=paginated_result.next_cursor,
        previous_cursor=paginated_result.previous_cursor,
        total_count=paginated_result.total_count,
    )


@router.get("/{video_id}", response_model=UnifiedContentResponse)
async def read_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int,
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    # 响应选项
    include_author: bool = Query(True, description="是否包含作者信息"),
    include_category: bool = Query(True, description="是否包含分类信息"),
    include_tags: bool = Query(True, description="是否包含标签信息"),
    include_stats: bool = Query(True, description="是否包含统计信息"),
    include_review: bool = Query(False, description="是否包含审核信息"),
    include_meta: bool = Query(True, description="是否包含元数据"),
) -> Any:
    """获取视频详情（重构版）
    - 统一响应格式
    - 可选择包含的信息
    - 权限控制
    """
    video = await video_service.check_permission(db, video_id, current_user)

    # 记录用户历史（访问次数已由中间件处理）
    try:
        if current_user:
            # 如果用户已登录，记录历史
            await record_user_history(
                db=db, user_id=current_user.id, content_type="video", content_id=video_id
            )
    except Exception as e:
        logger.error(f"记录视频历史失败: {e}")

    # 构建响应选项
    options = ContentResponseOptions(
        include_author=include_author,
        include_category=include_category,
        include_tags=include_tags,
        include_stats=include_stats,
        include_review=include_review,
        include_meta=include_meta,
    )

    # 构建统一响应
    current_user_id = current_user.id if current_user else None
    response = await video_response_service.build_video_response(
        db=db,
        video=video,
        options=options,
        current_user_id=current_user_id,
    )

    return response


@router.put("/{video_id}", response_model=schemas.Video)
async def update_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int,
    video_in: schemas.VideoUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """更新视频（统一端点）

    **支持的操作**：
    - 更新视频内容（标题、描述、URL、封面等）
    - 移动视频到其他文件夹（通过 folder_id）
    - 发布状态变更
    - 分类和标签管理

    **业务逻辑**：
    - 如果视频从未发布变为发布，需要创建审核记录
    - 如果已发布视频的内容有更新，需要重新审核
    - 未发布的视频（草稿）可以自由修改，不需要审核
    - 移动文件夹需要验证目标文件夹权限
    """
    # 检查更新权限
    video = await video_service.check_update_permission(db, video_id, current_user)

    video_in_dict = video_in.dict(exclude_unset=True)

    # 处理文件夹移动
    if "folder_id" in video_in_dict:
        # 检查移动权限（验证目标文件夹）
        await video_service.check_move_permission(
            db=db, video_id=video_id, folder_id=video_in.folder_id, current_user=current_user
        )

    # 处理发布状态变更
    if "is_published" in video_in_dict:
        video = await video_service.handle_publish_status(
            db=db, content=video, is_published=video_in.is_published
        )

    # 处理内容更新
    has_content_update = bool(video_in.title or video_in.description or video_in.url)
    video = await video_service.handle_content_update(
        db=db, content=video, has_content_update=has_content_update
    )

    # 更新视频
    video = await crud.video.update(db=db, db_obj=video, obj_in=video_in_dict)
    return video


@router.delete("/{video_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> None:
    """删除视频"""
    # 检查删除权限
    video = await video_service.check_delete_permission(db, video_id, current_user)
    # 删除视频及其关联数据
    await video_service.delete_content(db, video)


@router.get("/{video_id}/context", response_model=dict)
async def get_video_context(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int = Path(..., description="视频 ID"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
) -> Any:
    """获取视频上下文信息（优化版播放列表）

    **功能说明**：
    - 返回当前视频的上下文信息
    - 包含同文件夹的上一个/下一个视频
    - 提供文件夹信息用于完整播放列表

    **设计理念**：
    - 轻量级响应，只返回播放控制需要的信息
    - 完整播放列表通过 /folders/{folder_id}/videos 获取
    - 避免重复功能，保持 API 简洁
    """
    # 1. 获取并验证视频
    video = await video_service.check_permission(db, video_id, current_user)
    if not video:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="视频不存在或无权访问")

    # 2. 获取文件夹信息
    folder = None
    if video.folder_id:
        folder = await crud.video_folder.get(db=db, id=video.folder_id)

    # 3. 获取上一个和下一个视频
    prev_video = None
    next_video = None
    current_user_id = current_user.id if current_user else None

    if video.folder_id:
        # 获取同文件夹的相邻视频
        from sqlalchemy import and_

        # 构建基础查询条件
        base_conditions = [
            crud.video.model.folder_id == video.folder_id,
            crud.video.model.is_deleted.is_(False),
            crud.video.model.id != video.id,  # 排除当前视频
        ]

        # 根据权限过滤
        if current_user_id != video.author_id:
            base_conditions.extend(
                [
                    crud.video.model.is_published.is_(True),
                    crud.video.model.is_approved.is_(True),
                ]
            )

        # 获取上一个视频（ID 小于当前视频的最大 ID）
        prev_query = (
            select(crud.video.model)
            .where(and_(*base_conditions, crud.video.model.id < video.id))
            .order_by(crud.video.model.id.desc())
            .limit(1)
        )

        prev_result = await db.execute(prev_query)
        prev_video = prev_result.scalar_one_or_none()

        # 获取下一个视频（ID 大于当前视频的最小 ID）
        next_query = (
            select(crud.video.model)
            .where(and_(*base_conditions, crud.video.model.id > video.id))
            .order_by(crud.video.model.id.asc())
            .limit(1)
        )

        next_result = await db.execute(next_query)
        next_video = next_result.scalar_one_or_none()

    # 4. 构建响应
    return {
        "current_video": {
            "id": video.id,
            "title": video.title,
            "url": video.url,
            "cover_url": video.cover_url,
            "duration": video.duration,
        },
        "folder": {
            "id": folder.id if folder else None,
            "name": folder.name if folder else None,
            "path": folder.path if folder else None,
        }
        if folder
        else None,
        "navigation": {
            "prev_video": {
                "id": prev_video.id,
                "title": prev_video.title,
                "cover_url": prev_video.cover_url,
            }
            if prev_video
            else None,
            "next_video": {
                "id": next_video.id,
                "title": next_video.title,
                "cover_url": next_video.cover_url,
            }
            if next_video
            else None,
        },
        "playlist_url": f"/folders/{video.folder_id}/videos" if video.folder_id else None,
    }


@router.get("/recommendations", response_model=CursorPaginationResponse[UnifiedContentResponse])
async def get_video_recommendations(
    *,
    db: AsyncSession = Depends(deps.get_db),
    pagination: CursorPaginationParams = Depends(),
    algorithm_type: str = Query(
        "hybrid", description="推荐算法：collaborative, content_based, hot, hybrid"
    ),
    exclude_seen: bool = Query(True, description="是否排除已浏览视频"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    # 响应选项
    include_author: bool = Query(True, description="是否包含作者信息"),
    include_category: bool = Query(True, description="是否包含分类信息"),
    include_tags: bool = Query(True, description="是否包含标签信息"),
    include_stats: bool = Query(True, description="是否包含统计信息"),
    include_meta: bool = Query(True, description="是否包含元数据"),
) -> Any:
    """获取视频推荐列表

    **推荐策略**：
    - **登录用户**：基于用户画像的个性化推荐
    - **未登录用户**：热门视频推荐

    **算法类型**：
    - `collaborative`: 协同过滤推荐
    - `content_based`: 基于内容的推荐
    - `hot`: 热门推荐
    - `hybrid`: 混合推荐（默认）
    """
    # 获取推荐视频
    recommended_videos = await recommendation_service.get_recommended_videos(
        db=db,
        user_id=current_user.id if current_user else None,
        limit=pagination.size,
        exclude_video_id=None,
    )

    # 构建响应选项
    options = ContentResponseOptions(
        include_author=include_author,
        include_category=include_category,
        include_tags=include_tags,
        include_stats=include_stats,
        include_review=False,
        include_meta=include_meta,
    )

    # 构建响应项列表
    response_items = await UnifiedResponseService.build_content_list_response(
        db=db,
        contents=recommended_videos,
        options=options,
        total=len(recommended_videos),
    )

    # 构建分页响应（推荐暂不支持真正的分页，使用简单的截取）
    return CursorPaginationResponse(
        items=response_items,
        has_next=False,
        has_previous=False,
        next_cursor=None,
        previous_cursor=None,
        total_count=len(recommended_videos),
    )


@router.get("/{video_id}/similar", response_model=CursorPaginationResponse[UnifiedContentResponse])
async def get_similar_videos(
    video_id: int,
    *,
    db: AsyncSession = Depends(deps.get_db),
    limit: int = Query(10, ge=1, le=20, description="相似视频数量"),
    similarity_type: str = Query(
        "hybrid", description="相似度类型：tag_based, category_based, content_based, hybrid"
    ),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    # 响应选项
    include_author: bool = Query(True, description="是否包含作者信息"),
    include_category: bool = Query(True, description="是否包含分类信息"),
    include_tags: bool = Query(False, description="是否包含标签信息"),
    include_stats: bool = Query(True, description="是否包含统计信息"),
    include_meta: bool = Query(False, description="是否包含元数据"),
) -> Any:
    """获取相似视频推荐

    **相似度算法**：
    - `tag_based`: 基于标签相似度
    - `category_based`: 基于分类相似度
    - `content_based`: 基于内容特征相似度
    - `hybrid`: 混合相似度（默认）

    **权限控制**：
    - 支持游客访问
    - 只返回已发布且已审核的视频
    """
    # 1. 检查视频是否存在
    video = await crud.video.get(db=db, id=video_id)
    if not video:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="视频不存在")

    # 2. 获取相似内容
    similar_response = await recommendation_service.get_similar_content(
        db=db,
        content_type="video",
        content_id=video_id,
        user_id=current_user.id if current_user else None,
        limit=limit,
        algorithm=similarity_type,
    )

    # 3. 获取相似视频的详细信息
    if not similar_response.items:
        return CursorPaginationResponse(
            items=[],
            has_next=False,
            has_previous=False,
            next_cursor=None,
            previous_cursor=None,
            total_count=0,
        )

    video_ids = [item.content_id for item in similar_response.items if item.content_type == "video"]
    similar_videos = await crud.video.get_multi_by_ids(db=db, ids=video_ids)

    # 4. 权限过滤：只返回已发布且已审核的视频
    if current_user is None or current_user.id != video.author_id:
        similar_videos = [v for v in similar_videos if v.is_published and v.is_approved]

    # 5. 构建响应选项
    options = ContentResponseOptions(
        include_author=include_author,
        include_category=include_category,
        include_tags=include_tags,
        include_stats=include_stats,
        include_review=False,
        include_meta=include_meta,
    )

    # 6. 构建响应项列表
    response_items = await UnifiedResponseService.build_content_list_response(
        db=db,
        contents=similar_videos,
        options=options,
        total=len(similar_videos),
    )

    return CursorPaginationResponse(
        items=response_items,
        has_next=False,
        has_previous=False,
        next_cursor=None,
        previous_cursor=None,
        total_count=len(similar_videos),
    )


@router.get("/hot", response_model=CursorPaginationResponse[UnifiedContentResponse])
async def get_hot_videos(
    *,
    db: AsyncSession = Depends(deps.get_db),
    pagination: CursorPaginationParams = Depends(),
    time_range: str = Query("week", description="时间范围：today, week, month"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    # 响应选项
    include_author: bool = Query(True, description="是否包含作者信息"),
    include_category: bool = Query(True, description="是否包含分类信息"),
    include_tags: bool = Query(False, description="是否包含标签信息"),
    include_stats: bool = Query(True, description="是否包含统计信息"),
    include_meta: bool = Query(False, description="是否包含元数据"),
) -> Any:
    """获取热门视频列表

    **时间范围**：
    - `today`: 今日热门
    - `week`: 本周热门（默认）
    - `month`: 本月热门

    **排序规则**：
    - 综合考虑播放量、点赞数、评论数、分享数
    - 时间衰减算法，越新的视频权重越高
    """
    # 获取热门视频推荐项
    hot_items = await recommendation_service._hot_content_recommendation(
        db=db,
        user_id=current_user.id if current_user else None,
        limit=pagination.size,
        content_type="video",
        exclude_seen=False,  # 热门视频不排除已看过的
    )

    if not hot_items:
        return CursorPaginationResponse(
            items=[],
            has_next=False,
            has_previous=False,
            next_cursor=None,
            previous_cursor=None,
            total_count=0,
        )

    # 获取视频详细信息
    video_ids = [item.content_id for item in hot_items if item.content_type == "video"]
    hot_videos = await crud.video.get_multi_by_ids(db=db, ids=video_ids)

    # 权限过滤：只返回已发布且已审核的视频
    hot_videos = [v for v in hot_videos if v.is_published and v.is_approved]

    # 构建响应选项
    options = ContentResponseOptions(
        include_author=include_author,
        include_category=include_category,
        include_tags=include_tags,
        include_stats=include_stats,
        include_review=False,
        include_meta=include_meta,
    )

    # 构建响应项列表
    response_items = await UnifiedResponseService.build_content_list_response(
        db=db,
        contents=hot_videos,
        options=options,
        total=len(hot_videos),
    )

    return CursorPaginationResponse(
        items=response_items,
        has_next=False,
        has_previous=False,
        next_cursor=None,
        previous_cursor=None,
        total_count=len(hot_videos),
    )
