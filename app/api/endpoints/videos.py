from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Path, Query, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.api import deps
from app.api.permission_deps import PermissionDeps
from app.core.pagination import CursorPaginationParams, CursorPaginationResponse
from app.core.permission_system import (
    Action,
    ResourceType,
    Scope,
)
from app.core.permission_system import (
    Permission as PermissionObject,
)
from app.schemas.unified_response import ContentResponseOptions, UnifiedContentResponse
from app.schemas.video import VideoStatus
from app.services.content_service import video_service
from app.services.content_stats_service import ContentStatsService
from app.services.history_service import record_user_history
from app.services.logger import get_logger
from app.services.recommendation_service import RecommendationService
from app.services.video_folder_service import VideoFolderService
from app.services.video_response_service import video_response_service

# 初始化服务
content_stats_service = ContentStatsService()
recommendation_service = RecommendationService()

logger = get_logger(__name__)

router = APIRouter()


@router.post("/", response_model=schemas.Video)
async def create_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_in: schemas.VideoCreate,
    current_user: models.User = Depends(
        PermissionDeps.require_permission_enhanced(
            PermissionObject(resource=ResourceType.VIDEO, action=Action.CREATE, scope=Scope.OWN)
        )
    ),
) -> Any:
    """创建新视频
    - 未发布的视频（草稿）不需要审核
    - 发布的视频需要审核
    - 如果未指定folder_id，则使用用户的默认文件夹
    """
    # 处理文件夹ID
    if video_in.folder_id is None:
        # 获取用户的默认文件夹
        default_folder = await VideoFolderService.create_default_folder(db, current_user.id)
        video_in_dict = video_in.dict()
        video_in_dict["folder_id"] = default_folder.id
        video = await crud.video.create(db=db, obj_in=video_in_dict)
    else:
        # 验证文件夹是否存在且属于当前用户
        result = await db.execute(
            select(models.VideoFolder).where(
                models.VideoFolder.id == video_in.folder_id,
                models.VideoFolder.user_id == current_user.id,
                not models.VideoFolder.is_deleted,
            )
        )
        folder = result.scalar_one_or_none()
        if not folder:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="指定的文件夹不存在或不属于当前用户"
            )
        video = await crud.video.create(db=db, obj_in=video_in)

    # 处理发布状态
    video = await video_service.handle_publish_status(
        db=db, content=video, is_published=video.is_published
    )
    return video


@router.get("/", response_model=CursorPaginationResponse[UnifiedContentResponse])
async def read_videos(
    *,
    db: AsyncSession = Depends(deps.get_db),
    pagination: CursorPaginationParams = Depends(),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    # 筛选参数
    status: VideoStatus | None = Query(None, description="视频状态筛选"),
    category_id: int | None = Query(None, description="分类ID"),
    author_id: int | None = Query(None, description="作者ID"),
    folder_id: int | None = Query(None, description="文件夹ID"),
    # 响应选项
    include_author: bool = Query(True, description="是否包含作者信息"),
    include_category: bool = Query(True, description="是否包含分类信息"),
    include_tags: bool = Query(True, description="是否包含标签信息"),
    include_stats: bool = Query(True, description="是否包含统计信息"),
    include_review: bool = Query(False, description="是否包含审核信息"),
    include_meta: bool = Query(True, description="是否包含元数据"),
    include_total: bool = Query(False, description="是否包含总数（影响性能）"),
) -> Any:
    """获取视频列表（重构版）
    - 支持游标分页
    - 支持多种筛选条件
    - 统一响应格式
    - 权限控制
    """
    # 构建筛选条件
    filters = {}
    if status:
        filters["status"] = status.value
    if category_id:
        filters["category_id"] = category_id
    if author_id:
        filters["author_id"] = author_id
    if folder_id:
        filters["folder_id"] = folder_id

    # 获取分页数据
    paginated_result = await crud.video.get_paginated_videos(
        db=db,
        params=pagination,
        filters=filters,
        current_user=current_user,
        include_review=include_review,
        include_total=include_total,
    )

    # 构建响应选项
    options = ContentResponseOptions(
        include_author=include_author,
        include_category=include_category,
        include_tags=include_tags,
        include_stats=include_stats,
        include_review=include_review,
        include_meta=include_meta,
    )

    # 构建统一响应
    current_user_id = current_user.id if current_user else None
    response_items = await video_response_service.build_video_list_response(
        db=db,
        videos=paginated_result.items,
        options=options,
        current_user_id=current_user_id,
    )

    # 返回分页响应
    return CursorPaginationResponse(
        items=response_items,
        has_next=paginated_result.has_next,
        has_previous=paginated_result.has_previous,
        next_cursor=paginated_result.next_cursor,
        previous_cursor=paginated_result.previous_cursor,
        total_count=paginated_result.total_count,
    )


@router.get(
    "/users/{user_id}/folders",
    response_model=list[schemas.VideoFolder],
    summary="获取指定用户的文件夹列表",
)
async def get_user_folders(
    user_id: int,
    *,
    db: AsyncSession = Depends(deps.get_db),
    include_video_count: bool = Query(True, description="是否包含视频数量"),
    include_children: bool = Query(False, description="是否包含子文件夹信息"),
    path: str = Query("/", description="指定路径下的文件夹，默认为根路径"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
) -> Any:
    """
    获取指定用户的文件夹列表

    **权限控制**：
    - **用户本人**：可以访问所有文件夹（包括私有文件夹）
    - **其他用户**：只能访问公开文件夹
    - **未登录用户**：只能访问公开文件夹

    **功能特性**：
    - 支持按路径获取文件夹
    - 可选择是否包含视频数量统计
    - 可选择是否包含子文件夹信息
    - 自动权限过滤
    """
    # 1. 检查目标用户是否存在
    target_user = await crud.user.get(db=db, id=user_id)
    if not target_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")

    # 2. 权限判断
    is_own_folders = current_user and current_user.id == user_id

    # 权限检查：如果是访问他人的文件夹，需要有相应权限或只能看公开的
    if not is_own_folders:
        from app.core.permission_system import Permission, PermissionChecker

        # 检查是否有查看所有文件夹的权限
        has_view_all_permission = await PermissionChecker.check_permission(
            db,
            current_user,
            Permission(resource=ResourceType.FOLDER, action=Action.READ, scope=Scope.ALL),
        )
        # 如果没有管理权限，只能查看公开文件夹（后续会过滤）
        if not has_view_all_permission:
            user_info = current_user.id if current_user else "anonymous"
            logger.info(f"用户 {user_info} 访问用户 {user_id} 的公开文件夹")

    # 3. 获取用户的文件夹列表
    folders = await crud.video_folder.get_children(db, user_id=user_id, parent_path=path)

    # 4. 权限过滤：非本人只能看到公开文件夹
    if not is_own_folders:
        folders = [folder for folder in folders if folder.is_public]

    # 5. 补充文件夹信息
    for folder in folders:
        if include_video_count:
            # 获取文件夹中的视频数量
            from sqlalchemy import func

            folder.video_count = (
                await db.execute(
                    select(func.count())
                    .select_from(crud.video.model)
                    .where(
                        crud.video.model.folder_id == folder.id,
                        crud.video.model.is_deleted.is_(False),
                    )
                )
            ).scalar()

        if include_children:
            # 检查是否有子文件夹
            children = await crud.video_folder.get_children(
                db, user_id=user_id, parent_path=folder.path
            )
            # 如果不是本人，也要过滤子文件夹的公开性
            if not is_own_folders:
                children = [child for child in children if child.is_public]
            folder.has_children = bool(children)

    return folders


@router.get("/my", response_model=CursorPaginationResponse[UnifiedContentResponse])
async def read_my_videos(
    *,
    db: AsyncSession = Depends(deps.get_db),
    pagination: CursorPaginationParams = Depends(),
    current_user: models.User = Depends(deps.get_current_active_user),
    # 筛选参数
    status: VideoStatus | None = Query(VideoStatus.ALL, description="视频状态筛选"),
    folder_id: int | None = Query(None, description="文件夹ID"),
    # 响应选项
    include_author: bool = Query(True, description="是否包含作者信息"),
    include_category: bool = Query(True, description="是否包含分类信息"),
    include_tags: bool = Query(True, description="是否包含标签信息"),
    include_stats: bool = Query(True, description="是否包含统计信息"),
    include_review: bool = Query(True, description="是否包含审核信息"),
    include_meta: bool = Query(True, description="是否包含元数据"),
    include_total: bool = Query(False, description="是否包含总数（影响性能）"),
) -> Any:
    """获取当前用户的视频列表（重构版）
    - 支持游标分页
    - 支持状态筛选（草稿、已发布、待审核等）
    - 支持文件夹筛选
    - 统一响应格式
    """
    # 获取分页数据
    paginated_result = await crud.video.get_paginated_videos_by_user(
        db=db,
        user_id=current_user.id,
        params=pagination,
        status_filter=status,
        is_own_videos=True,
        include_review=include_review,
        include_total=include_total,
    )

    # 如果指定了文件夹筛选，需要额外过滤
    if folder_id:
        paginated_result.items = [
            video for video in paginated_result.items if video.folder_id == folder_id
        ]

    # 构建响应选项
    options = ContentResponseOptions(
        include_author=include_author,
        include_category=include_category,
        include_tags=include_tags,
        include_stats=include_stats,
        include_review=include_review,
        include_meta=include_meta,
    )

    # 构建统一响应
    response_items = await video_response_service.build_video_list_response(
        db=db,
        videos=paginated_result.items,
        options=options,
        current_user_id=current_user.id,
    )

    # 返回分页响应
    return CursorPaginationResponse(
        items=response_items,
        has_next=paginated_result.has_next,
        has_previous=paginated_result.has_previous,
        next_cursor=paginated_result.next_cursor,
        previous_cursor=paginated_result.previous_cursor,
        total_count=paginated_result.total_count,
    )


@router.get("/{video_id}", response_model=UnifiedContentResponse)
async def read_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int,
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    # 响应选项
    include_author: bool = Query(True, description="是否包含作者信息"),
    include_category: bool = Query(True, description="是否包含分类信息"),
    include_tags: bool = Query(True, description="是否包含标签信息"),
    include_stats: bool = Query(True, description="是否包含统计信息"),
    include_review: bool = Query(False, description="是否包含审核信息"),
    include_meta: bool = Query(True, description="是否包含元数据"),
) -> Any:
    """获取视频详情（重构版）
    - 统一响应格式
    - 可选择包含的信息
    - 权限控制
    """
    video = await video_service.check_permission(db, video_id, current_user)

    # 记录用户历史（访问次数已由中间件处理）
    try:
        if current_user:
            # 如果用户已登录，记录历史
            await record_user_history(
                db=db, user_id=current_user.id, content_type="video", content_id=video_id
            )
    except Exception as e:
        logger.error(f"记录视频历史失败: {e}")

    # 构建响应选项
    options = ContentResponseOptions(
        include_author=include_author,
        include_category=include_category,
        include_tags=include_tags,
        include_stats=include_stats,
        include_review=include_review,
        include_meta=include_meta,
    )

    # 构建统一响应
    current_user_id = current_user.id if current_user else None
    response = await video_response_service.build_video_response(
        db=db,
        video=video,
        options=options,
        current_user_id=current_user_id,
    )

    return response


@router.put("/{video_id}", response_model=schemas.Video)
async def update_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int,
    video_in: schemas.VideoUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """更新视频
    - 如果视频从未发布变为发布，需要创建审核记录
    - 如果已发布视频的内容有更新，需要重新审核
    - 未发布的视频（草稿）可以自由修改，不需要审核
    """
    # 检查更新权限
    video = await video_service.check_update_permission(db, video_id, current_user)

    video_in_dict = video_in.dict(exclude_unset=True)

    # 处理发布状态变更
    if "is_published" in video_in_dict:
        video = await video_service.handle_publish_status(
            db=db, content=video, is_published=video_in.is_published
        )

    # 处理内容更新
    has_content_update = bool(video_in.title or video_in.description or video_in.url)
    video = await video_service.handle_content_update(
        db=db, content=video, has_content_update=has_content_update
    )

    # 更新视频
    video = await crud.video.update(db=db, db_obj=video, obj_in=video_in_dict)
    return video


@router.put("/{video_id}/move", response_model=schemas.Video)
async def move_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int = Path(..., description="视频 ID（必须为整数）"),
    folder_id: int = Query(..., description="目标文件夹 ID（必须为整数）"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """移动视频到指定文件夹"""
    # 检查移动权限
    video = await video_service.check_move_permission(
        db=db, video_id=video_id, folder_id=folder_id, current_user=current_user
    )
    # 移动视频
    video = await crud.video.update(
        db=db,
        db_obj=video,
        obj_in={"folder_id": folder_id},
    )
    return video


@router.delete("/{video_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> None:
    """删除视频"""
    # 检查删除权限
    video = await video_service.check_delete_permission(db, video_id, current_user)
    # 删除视频及其关联数据
    await video_service.delete_content(db, video)


@router.get("/{video_id}/playlist", response_model=dict)
async def get_play_list(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int = Path(..., description="视频 ID（必须为整数）"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    limit: int = Query(10, ge=1, le=20, description="播放列表视频数量"),
    # 响应选项
    include_author: bool = Query(True, description="是否包含作者信息"),
    include_category: bool = Query(True, description="是否包含分类信息"),
    include_tags: bool = Query(False, description="是否包含标签信息"),
    include_stats: bool = Query(True, description="是否包含统计信息"),
    include_review: bool = Query(False, description="是否包含审核信息"),
    include_meta: bool = Query(False, description="是否包含元数据"),
) -> Any:
    """获取视频播放列表（重构版）
    - 统一响应格式
    - 可选择包含的信息
    - 只展示同文件夹的视频
    - 根据用户身份判断是否展示未发布的视频
    """
    # 1. 获取并验证视频
    video = await video_service.check_permission(db, video_id, current_user)
    if not video:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="视频不存在或无权访问")

    # 2. 获取同文件夹的视频列表
    videos = []
    current_user_id = current_user.id if current_user else None
    logger.info(f"获取视频播放列表，video_folder_id={video.folder_id}")

    if video.folder_id:
        videos = await VideoFolderService.get_videos_in_same_folder(
            db,
            video_id=video.id,
            folder_id=video.folder_id,
            limit=limit,
            current_user_id=current_user_id,
        )

    # 构建响应选项
    options = ContentResponseOptions(
        include_author=include_author,
        include_category=include_category,
        include_tags=include_tags,
        include_stats=include_stats,
        include_review=include_review,
        include_meta=include_meta,
    )

    # 构建当前视频响应
    current_video_response = await video_response_service.build_video_response(
        db=db,
        video=video,
        options=options,
        current_user_id=current_user_id,
    )

    # 构建播放列表响应
    playlist_response = await video_response_service.build_video_list_response(
        db=db,
        videos=videos,
        options=options,
        current_user_id=current_user_id,
    )

    return {
        "current_video": current_video_response,
        "playlist": playlist_response,
        "list_type": "folder",
        "total": len(videos),
    }
