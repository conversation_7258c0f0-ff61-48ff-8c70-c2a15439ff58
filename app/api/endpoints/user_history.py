from typing import Any

from fastapi import APIRouter, Depends, Query, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models
from app.api import deps
from app.core.pagination import CursorPaginationParams, CursorPaginationResponse, CursorPaginator
from app.services.history_cache_service import history_service

router = APIRouter()


@router.get("/favorites/with-content", response_model=CursorPaginationResponse)
async def get_user_favorite_history_with_content(
    *,
    db: AsyncSession = Depends(deps.get_db),
    content_type: str | None = Query(None, description="内容类型筛选: article, video"),
    cursor: str | None = Query(None, description="游标位置，用于获取下一页数据"),
    size: int = Query(20, ge=1, le=100, description="每页大小，范围1-100"),
    order_by: str = Query("id", description="排序字段"),
    order_direction: str = Query("desc", pattern="^(asc|desc)$", description="排序方向：asc或desc"),
    include_total: bool = Query(False, description="是否包含总数量（会增加查询成本）"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取用户收藏历史记录（包含内容详情）"""
    # 构建基础查询
    query = select(models.Favorite).where(models.Favorite.user_id == current_user.id)

    # 添加内容类型过滤
    if content_type:
        query = query.where(models.Favorite.content_type == content_type)

    # 创建分页参数
    pagination_params = CursorPaginationParams(
        cursor=cursor,
        size=size,
        order_by=order_by,
        order_direction=order_direction,
        include_total=include_total,
    )

    # 使用游标分页器
    paginator = CursorPaginator()
    paginated_result = await paginator.paginate_query(
        db=db, query=query, params=pagination_params, model=models.Favorite
    )

    favorites = paginated_result.items

    # 使用批量查询优化内容获取
    # 1. 收集所有文章ID和视频ID
    article_ids = [f.content_id for f in favorites if f.content_type == "article"]
    video_ids = [f.content_id for f in favorites if f.content_type == "video"]

    # 2. 批量查询文章和视频
    articles = {}
    videos = {}

    if article_ids:
        # 使用SQLAlchemy的in_操作符批量查询文章
        article_result = await db.execute(
            select(models.Article).where(models.Article.id.in_(article_ids))
        )
        articles = {article.id: article for article in article_result.scalars().all()}

    if video_ids:
        # 使用SQLAlchemy的in_操作符批量查询视频
        video_result = await db.execute(select(models.Video).where(models.Video.id.in_(video_ids)))
        videos = {video.id: video for video in video_result.scalars().all()}

    # 3. 遍历收藏记录，从映射表中获取内容详情
    favorites_with_content = []
    for favorite in favorites:
        favorite_dict = {
            "id": favorite.id,
            "user_id": favorite.user_id,
            "content_type": favorite.content_type,
            "content_id": favorite.content_id,
            "note": favorite.note,
            "created_at": favorite.created_at.isoformat() if favorite.created_at else None,
        }

        if favorite.content_type == "article" and favorite.content_id in articles:
            content = articles[favorite.content_id]
            favorite_dict.update(
                {
                    "content_title": content.title,
                    "content_author": content.author.username if content.author else None,
                    "content_created_at": content.created_at.isoformat()
                    if content.created_at
                    else None,
                }
            )
        elif favorite.content_type == "video" and favorite.content_id in videos:
            content = videos[favorite.content_id]
            favorite_dict.update(
                {
                    "content_title": content.title,
                    "content_author": content.author.username if content.author else None,
                    "content_created_at": content.created_at.isoformat()
                    if content.created_at
                    else None,
                }
            )

        favorites_with_content.append(favorite_dict)

    return CursorPaginationResponse(
        items=favorites_with_content,
        has_next=paginated_result.has_next,
        has_previous=paginated_result.has_previous,
        next_cursor=paginated_result.next_cursor,
        previous_cursor=paginated_result.previous_cursor,
        total_count=paginated_result.total_count,
    )


@router.get("/stats", response_model=dict)
async def get_user_activity_stats(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取用户活动统计"""
    # 获取点赞统计
    total_likes = await crud.like.get_user_like_count(db, user_id=current_user.id)
    article_likes = await crud.like.get_user_like_count(
        db, user_id=current_user.id, content_type="article"
    )
    video_likes = await crud.like.get_user_like_count(
        db, user_id=current_user.id, content_type="video"
    )

    # 获取收藏统计
    total_favorites = await crud.favorite.get_user_favorite_count(db, user_id=current_user.id)
    article_favorites = await crud.favorite.get_user_favorite_count(
        db, user_id=current_user.id, content_type="article"
    )
    video_favorites = await crud.favorite.get_user_favorite_count(
        db, user_id=current_user.id, content_type="video"
    )

    return {
        "likes": {
            "total": total_likes,
            "articles": article_likes,
            "videos": video_likes,
        },
        "favorites": {
            "total": total_favorites,
            "articles": article_favorites,
            "videos": video_favorites,
        },
    }


@router.get("/recent-activity", response_model=dict)
async def get_user_recent_activity(
    *,
    db: AsyncSession = Depends(deps.get_db),
    limit: int = Query(10, ge=1, le=50, description="返回的最大记录数"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取用户最近活动"""
    # 获取最近的点赞
    recent_likes = await crud.like.get_user_likes(db, user_id=current_user.id, skip=0, limit=limit)

    # 获取最近的收藏
    recent_favorites = await crud.favorite.get_user_favorites(
        db, user_id=current_user.id, skip=0, limit=limit
    )

    return {
        "recent_likes": [
            {
                "id": like.id,
                "content_type": like.content_type,
                "content_id": like.content_id,
                "created_at": like.created_at,
            }
            for like in recent_likes
        ],
        "recent_favorites": [
            {
                "id": favorite.id,
                "content_type": favorite.content_type,
                "content_id": favorite.content_id,
                "note": favorite.note,
                "created_at": favorite.created_at,
            }
            for favorite in recent_favorites
        ],
    }


@router.get("/history", response_model=CursorPaginationResponse)
async def get_user_history(
    *,
    db: AsyncSession = Depends(deps.get_db),
    content_type: str | None = Query(None, description="内容类型筛选: article, video"),
    cursor: str | None = Query(None, description="游标位置，用于获取下一页数据"),
    size: int = Query(20, ge=1, le=100, description="每页大小，范围1-100"),
    order_by: str = Query("id", description="排序字段"),
    order_direction: str = Query("desc", pattern="^(asc|desc)$", description="排序方向：asc或desc"),
    include_total: bool = Query(False, description="是否包含总数量（会增加查询成本）"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取用户浏览历史记录"""
    # 构建基础查询
    query = select(models.UserBrowseHistory).where(
        models.UserBrowseHistory.user_id == current_user.id
    )

    # 添加内容类型过滤
    if content_type:
        query = query.where(models.UserBrowseHistory.content_type == content_type)

    # 创建分页参数
    pagination_params = CursorPaginationParams(
        cursor=cursor,
        size=size,
        order_by=order_by,
        order_direction=order_direction,
        include_total=include_total,
    )

    # 使用游标分页器
    paginator = CursorPaginator()
    paginated_result = await paginator.paginate_query(
        db=db, query=query, params=pagination_params, model=models.UserBrowseHistory
    )

    histories = paginated_result.items

    # 使用批量查询优化内容获取
    # 1. 收集所有文章ID和视频ID
    article_ids = [h.content_id for h in histories if h.content_type == "article"]
    video_ids = [h.content_id for h in histories if h.content_type == "video"]

    # 2. 批量查询文章和视频
    articles = {}
    videos = {}

    if article_ids:
        # 使用SQLAlchemy的in_操作符批量查询文章
        article_result = await db.execute(
            select(models.Article).where(models.Article.id.in_(article_ids))
        )
        articles = {article.id: article for article in article_result.scalars().all()}

    if video_ids:
        # 使用SQLAlchemy的in_操作符批量查询视频
        video_result = await db.execute(select(models.Video).where(models.Video.id.in_(video_ids)))
        videos = {video.id: video for video in video_result.scalars().all()}

    # 3. 遍历历史记录，从映射表中获取内容详情
    histories_with_content = []
    for history in histories:
        history_dict = {
            "id": history.id,
            "user_id": history.user_id,
            "content_type": history.content_type,
            "content_id": history.content_id,
            "created_at": history.created_at.isoformat() if history.created_at else None,
        }

        if history.content_type == "article" and history.content_id in articles:
            content = articles[history.content_id]
            history_dict.update(
                {
                    "content_title": content.title,
                    "content_author": content.author.username if content.author else None,
                    "content_created_at": content.created_at.isoformat()
                    if content.created_at
                    else None,
                }
            )
        elif history.content_type == "video" and history.content_id in videos:
            content = videos[history.content_id]
            history_dict.update(
                {
                    "content_title": content.title,
                    "content_author": content.author.username if content.author else None,
                    "content_created_at": content.created_at.isoformat()
                    if content.created_at
                    else None,
                }
            )

        histories_with_content.append(history_dict)

    return CursorPaginationResponse(
        items=histories_with_content,
        has_next=paginated_result.has_next,
        has_previous=paginated_result.has_previous,
        next_cursor=paginated_result.next_cursor,
        previous_cursor=paginated_result.previous_cursor,
        total_count=paginated_result.total_count,
    )


@router.delete("/history", status_code=status.HTTP_204_NO_CONTENT)
async def clear_user_history(
    *,
    db: AsyncSession = Depends(deps.get_db),
    content_type: str | None = Query(None, description="内容类型过滤：article, video"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> None:
    """清空用户历史浏览数据"""
    # 清空数据库中的历史记录
    await crud.history.remove_by_user(db, user_id=current_user.id, content_type=content_type)

    # 清空缓存
    if content_type:
        await history_service.clear_user_content_type_cache(current_user.id, content_type)
    else:
        await history_service.clear_user_cache(current_user.id)
