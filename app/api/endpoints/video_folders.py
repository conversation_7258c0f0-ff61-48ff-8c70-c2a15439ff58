from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud
from app.api import deps
from app.core.permission_system import (
    Action,
    PermissionChecker,
    ResourceType,
    Scope,
)
from app.core.permission_system import (
    Permission as PermissionObject,
)
from app.models.user import User
from app.schemas.video_folder import (
    VideoFolder,
    VideoFolderCreate,
    VideoFolderTree,
    VideoFolderUpdate,
)

router = APIRouter()


@router.post("/", response_model=VideoFolder)
async def create_folder(
    *,
    db: AsyncSession = Depends(deps.get_db),
    folder_in: VideoFolderCreate,
    current_user: User = Depends(deps.get_current_user),
) -> VideoFolder:
    """创建视频文件夹"""
    # 检查父文件夹是否存在
    parent_path = "/".join(folder_in.path.rstrip("/").split("/")[:-1]) or "/"
    parent_id = None
    if parent_path != "/":
        parent_folder = await crud.video_folder.get_by_path(
            db, user_id=current_user.id, path=parent_path
        )
        if not parent_folder:
            raise HTTPException(
                status_code=404,
                detail=f"Parent folder {parent_path} not found",
            )
        parent_id = parent_folder.id

    # 检查文件夹是否已存在
    existing_folder = await crud.video_folder.get_by_path(
        db, user_id=current_user.id, path=folder_in.path
    )
    if existing_folder:
        raise HTTPException(
            status_code=400,
            detail=f"Folder {folder_in.path} already exists",
        )

    # 创建文件夹，设置默认值
    folder = await crud.video_folder.create(
        db,
        obj_in={
            **folder_in.dict(),
            "user_id": current_user.id,
            "parent_id": parent_id,
        },
    )
    return folder


@router.post("/default", response_model=VideoFolder)
async def create_default_folder(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
) -> VideoFolder:
    """创建默认视频文件夹（如果不存在）"""
    from app.services.video_folder_service import VideoFolderService

    # 检查用户是否已有默认文件夹
    existing_default = await db.execute(
        select(crud.video_folder.model).where(
            crud.video_folder.model.user_id == current_user.id,
            crud.video_folder.model.is_default == True,
            crud.video_folder.model.is_deleted == False,
        )
    )
    existing_default = existing_default.scalar_one_or_none()

    if existing_default:
        return existing_default

    # 创建默认文件夹
    default_folder = await VideoFolderService.create_default_folder(db._session, current_user.id)
    # 注意：create_default_folder 方法内部已经调用了 commit，这里不需要再次提交

    return default_folder


@router.get("/", response_model=list[VideoFolder])
async def get_folders(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    path: str = "/",
) -> list[VideoFolder]:
    """获取指定路径下的文件夹列表"""
    folders = await crud.video_folder.get_children(db, user_id=current_user.id, parent_path=path)
    # 补充每个文件夹的视频数量和是否有子文件夹
    for folder in folders:
        # 获取文件夹中的视频数量
        folder.video_count = (
            await db.execute(
                select(func.count())
                .select_from(crud.video.model)
                .where(crud.video.model.folder_id == folder.id)
            )
        ).scalar()
        # 检查是否有子文件夹
        folder.has_children = bool(
            await crud.video_folder.get_children(
                db, user_id=current_user.id, parent_path=folder.path
            )
        )
    return folders


@router.get("/tree", response_model=list[VideoFolderTree])
async def get_folder_tree(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
) -> list[VideoFolderTree]:
    """获取文件夹树形结构"""

    async def build_tree(path: str) -> list[VideoFolderTree]:
        folders = await crud.video_folder.get_children(
            db, user_id=current_user.id, parent_path=path
        )
        result = []
        for folder in folders:
            # 获取文件夹中的视频数量
            folder.video_count = (
                await db.execute(
                    select(func.count())
                    .select_from(crud.video.model)
                    .where(crud.video.model.folder_id == folder.id)
                )
            ).scalar()
            # 递归获取子文件夹
            children = await build_tree(folder.path)
            folder.has_children = bool(children)
            folder_tree = VideoFolderTree(
                **folder.__dict__,
                children=children,
            )
            result.append(folder_tree)
        return result

    return await build_tree("/")


@router.put("/{folder_id}", response_model=VideoFolder)
async def update_folder(
    *,
    db: AsyncSession = Depends(deps.get_db),
    folder_id: int,
    folder_in: VideoFolderUpdate,
    current_user: User = Depends(deps.get_current_user),
) -> VideoFolder:
    """更新文件夹"""
    folder = await crud.video_folder.get(db, id=folder_id)
    if not folder:
        raise HTTPException(status_code=404, detail="Folder not found")

    await PermissionChecker.require_permission(
        db,
        current_user,
        PermissionObject(resource=ResourceType.FOLDER, action=Action.UPDATE, scope=Scope.OWN),
        resource=folder,
    )

    folder = await crud.video_folder.update(db, db_obj=folder, obj_in=folder_in)
    return folder


@router.delete("/{folder_id}")
async def delete_folder(
    *,
    db: AsyncSession = Depends(deps.get_db),
    folder_id: int,
    current_user: User = Depends(deps.get_current_user),
    force: bool = False,  # 是否强制删除（物理删除）
) -> dict:
    """删除文件夹

    默认为软删除，将文件夹标记为已删除，并将其中的视频移动到默认文件夹
    如果指定force=true，则物理删除文件夹（需要满足文件夹为空且无子文件夹）
    """
    from app.services.video_folder_service import VideoFolderService

    folder = await crud.video_folder.get(db, id=folder_id)
    if not folder:
        raise HTTPException(status_code=404, detail="Folder not found")

    await PermissionChecker.require_permission(
        db,
        current_user,
        PermissionObject(resource=ResourceType.FOLDER, action=Action.DELETE, scope=Scope.OWN),
        resource=folder,
    )

    # 不允许删除默认文件夹
    if folder.is_default:
        raise HTTPException(
            status_code=400,
            detail="Cannot delete default folder",
        )

    if force:
        # 强制删除（物理删除）需要检查文件夹是否为空
        videos = (
            (
                await db.execute(
                    select(crud.video.model).where(crud.video.model.folder_id == folder_id)
                )
            )
            .scalars()
            .all()
        )
        if videos:
            raise HTTPException(
                status_code=400,
                detail="Cannot delete non-empty folder",
            )

        # 检查是否有子文件夹
        children = await crud.video_folder.get_children(
            db, user_id=current_user.id, parent_path=folder.path
        )
        if children:
            raise HTTPException(
                status_code=400,
                detail="Cannot delete folder with sub-folders",
            )

        await crud.video_folder.remove(db, id=folder_id)
        return {"message": "Folder deleted successfully"}
    else:
        # 软删除
        # 使用服务层方法进行软删除，会自动处理子文件夹和将视频移动到默认文件夹
        success = await VideoFolderService.soft_delete_folder(
            db._session, folder_id, current_user.id
        )
        if not success:
            raise HTTPException(
                status_code=400,
                detail="Failed to delete folder",
            )
        return {"message": "Folder soft deleted successfully"}


@router.get("/users/{user_id}", response_model=list[VideoFolder])
async def get_user_folders(
    user_id: int,
    *,
    db: AsyncSession = Depends(deps.get_db),
    include_video_count: bool = Query(True, description="是否包含视频数量"),
    include_children: bool = Query(False, description="是否包含子文件夹信息"),
    path: str = Query("/", description="指定路径下的文件夹，默认为根路径"),
    current_user: User | None = Depends(deps.get_current_user_optional),
) -> list[VideoFolder]:
    """
    获取指定用户的文件夹列表

    **权限控制**：
    - **用户本人**：可以访问所有文件夹（包括私有文件夹）
    - **其他用户**：只能访问公开文件夹
    - **未登录用户**：只能访问公开文件夹

    **功能特性**：
    - 支持按路径获取文件夹
    - 可选择是否包含视频数量统计
    - 可选择是否包含子文件夹信息
    - 自动权限过滤
    """
    # 1. 检查目标用户是否存在
    target_user = await crud.user.get(db=db, id=user_id)
    if not target_user:
        raise HTTPException(status_code=404, detail="用户不存在")

    # 2. 权限判断
    is_own_folders = current_user and current_user.id == user_id

    # 权限检查：如果是访问他人的文件夹，需要有相应权限或只能看公开的
    if not is_own_folders:
        # 检查是否有查看所有文件夹的权限
        has_view_all_permission = await PermissionChecker.check_permission(
            db,
            current_user,
            PermissionObject(resource=ResourceType.FOLDER, action=Action.READ, scope=Scope.ALL),
        )
        # 如果没有管理权限且不是本人，只能查看公开文件夹
        if not has_view_all_permission and not current_user:
            # 未登录用户只能查看公开文件夹
            pass

    # 3. 获取用户的文件夹列表
    folders = await crud.video_folder.get_children(db, user_id=user_id, parent_path=path)

    # 4. 权限过滤：非本人只能看到公开文件夹
    if not is_own_folders:
        folders = [folder for folder in folders if folder.is_public]

    # 5. 补充文件夹信息
    for folder in folders:
        if include_video_count:
            # 获取文件夹中的视频数量
            folder.video_count = (
                await db.execute(
                    select(func.count())
                    .select_from(crud.video.model)
                    .where(
                        crud.video.model.folder_id == folder.id,
                        crud.video.model.is_deleted.is_(False),
                    )
                )
            ).scalar()

        if include_children:
            # 检查是否有子文件夹
            children = await crud.video_folder.get_children(
                db, user_id=user_id, parent_path=folder.path
            )
            # 如果不是本人，也要过滤子文件夹的公开性
            if not is_own_folders:
                children = [child for child in children if child.is_public]
            folder.has_children = bool(children)

    return folders
