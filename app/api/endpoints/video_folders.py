from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud
from app.api import deps
from app.core.permission_system import (
    Action,
    PermissionChecker,
    ResourceType,
    Scope,
)
from app.core.permission_system import (
    Permission as PermissionObject,
)
from app.models.user import User
from app.schemas.video_folder import (
    VideoFolder,
    VideoFolderCreate,
    VideoFolderTree,
    VideoFolderUpdate,
)

router = APIRouter()


@router.post("/", response_model=VideoFolder)
async def create_folder(
    *,
    db: AsyncSession = Depends(deps.get_db),
    folder_in: VideoFolderCreate,
    current_user: User = Depends(deps.get_current_user),
) -> VideoFolder:
    """创建视频文件夹"""
    # 检查父文件夹是否存在
    parent_path = "/".join(folder_in.path.rstrip("/").split("/")[:-1]) or "/"
    parent_id = None
    if parent_path != "/":
        parent_folder = await crud.video_folder.get_by_path(
            db, user_id=current_user.id, path=parent_path
        )
        if not parent_folder:
            raise HTTPException(
                status_code=404,
                detail=f"Parent folder {parent_path} not found",
            )
        parent_id = parent_folder.id

    # 检查文件夹是否已存在
    existing_folder = await crud.video_folder.get_by_path(
        db, user_id=current_user.id, path=folder_in.path
    )
    if existing_folder:
        raise HTTPException(
            status_code=400,
            detail=f"Folder {folder_in.path} already exists",
        )

    # 创建文件夹，设置默认值
    folder = await crud.video_folder.create(
        db,
        obj_in={
            **folder_in.dict(),
            "user_id": current_user.id,
            "parent_id": parent_id,
        },
    )
    return folder


@router.get("/tree", response_model=list[VideoFolderTree])
async def get_folder_tree(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
) -> list[VideoFolderTree]:
    """获取文件夹树形结构"""

    async def build_tree(path: str) -> list[VideoFolderTree]:
        folders = await crud.video_folder.get_children(
            db, user_id=current_user.id, parent_path=path
        )
        result = []
        for folder in folders:
            # 获取文件夹中的视频数量
            folder.video_count = (
                await db.execute(
                    select(func.count())
                    .select_from(crud.video.model)
                    .where(crud.video.model.folder_id == folder.id)
                )
            ).scalar()
            # 递归获取子文件夹
            children = await build_tree(folder.path)
            folder.has_children = bool(children)
            folder_tree = VideoFolderTree(
                **folder.__dict__,
                children=children,
            )
            result.append(folder_tree)
        return result

    return await build_tree("/")


@router.put("/{folder_id}", response_model=VideoFolder)
async def update_folder(
    *,
    db: AsyncSession = Depends(deps.get_db),
    folder_id: int,
    folder_in: VideoFolderUpdate,
    current_user: User = Depends(deps.get_current_user),
) -> VideoFolder:
    """更新文件夹"""
    folder = await crud.video_folder.get(db, id=folder_id)
    if not folder:
        raise HTTPException(status_code=404, detail="Folder not found")

    await PermissionChecker.require_permission(
        db,
        current_user,
        PermissionObject(resource=ResourceType.FOLDER, action=Action.UPDATE, scope=Scope.OWN),
        resource=folder,
    )

    folder = await crud.video_folder.update(db, db_obj=folder, obj_in=folder_in)
    return folder


@router.delete("/{folder_id}")
async def delete_folder(
    *,
    db: AsyncSession = Depends(deps.get_db),
    folder_id: int,
    current_user: User = Depends(deps.get_current_user),
    force: bool = False,  # 是否强制删除（物理删除）
) -> dict:
    """删除文件夹

    默认为软删除，将文件夹标记为已删除，并将其中的视频移动到默认文件夹
    如果指定force=true，则物理删除文件夹（需要满足文件夹为空且无子文件夹）
    """
    from app.services.video_folder_service import VideoFolderService

    folder = await crud.video_folder.get(db, id=folder_id)
    if not folder:
        raise HTTPException(status_code=404, detail="Folder not found")

    await PermissionChecker.require_permission(
        db,
        current_user,
        PermissionObject(resource=ResourceType.FOLDER, action=Action.DELETE, scope=Scope.OWN),
        resource=folder,
    )

    # 不允许删除默认文件夹
    if folder.is_default:
        raise HTTPException(
            status_code=400,
            detail="Cannot delete default folder",
        )

    if force:
        # 强制删除（物理删除）需要检查文件夹是否为空
        videos = (
            (
                await db.execute(
                    select(crud.video.model).where(crud.video.model.folder_id == folder_id)
                )
            )
            .scalars()
            .all()
        )
        if videos:
            raise HTTPException(
                status_code=400,
                detail="Cannot delete non-empty folder",
            )

        # 检查是否有子文件夹
        children = await crud.video_folder.get_children(
            db, user_id=current_user.id, parent_path=folder.path
        )
        if children:
            raise HTTPException(
                status_code=400,
                detail="Cannot delete folder with sub-folders",
            )

        await crud.video_folder.remove(db, id=folder_id)
        return {"message": "Folder deleted successfully"}
    else:
        # 软删除
        # 使用服务层方法进行软删除，会自动处理子文件夹和将视频移动到默认文件夹
        success = await VideoFolderService.soft_delete_folder(
            db._session, folder_id, current_user.id
        )
        if not success:
            raise HTTPException(
                status_code=400,
                detail="Failed to delete folder",
            )
        return {"message": "Folder soft deleted successfully"}
