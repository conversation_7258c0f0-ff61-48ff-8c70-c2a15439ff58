"""推荐系统API接口"""

from datetime import datetime, timedelta
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.api import deps
from app.api.permission_deps import PermissionDeps
from app.core.permission_system import (
    Action,
    PermissionChecker,
    ResourceType,
    Scope,
)
from app.core.permission_system import (
    Permission as PermissionObject,
)
from app.services.recommendation_cache_service import recommendation_cache_service
from app.services.recommendation_service import recommendation_service

router = APIRouter()


@router.get("/", response_model=schemas.RecommendationResponse)
async def get_recommendations(
    *,
    db: AsyncSession = Depends(deps.get_db),
    algorithm_type: str | None = Query(
        None, description="推荐算法类型：collaborative, content_based, hot, hybrid"
    ),
    content_type: str | None = Query(None, description="内容类型过滤：article, video"),
    position: str | None = Query(
        None, description="推荐位置：homepage, category_page, article_detail等"
    ),
    limit: int = Query(10, ge=1, le=50, description="推荐数量"),
    exclude_seen: bool = Query(True, description="是否排除已浏览内容"),
    use_cache: bool = Query(True, description="是否使用缓存"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
) -> Any:
    """获取推荐内容（支持游客访问，未登录用户将获得热门内容推荐）"""

    # 如果用户未登录，降级为热门内容推荐
    if current_user is None:
        # 对于游客，强制使用热门推荐算法
        algorithm_type = "hot"
        exclude_seen = False  # 游客无法排除已浏览内容

        # 尝试从缓存获取热门内容
        if use_cache:
            cached_hot = await recommendation_cache_service.get_hot_content(
                content_type=content_type or "article",
                time_range="week",
            )

            if cached_hot:
                # 转换为推荐响应格式
                from app.schemas.recommendation import RecommendationItem

                items = [
                    RecommendationItem(
                        content_type=item.get("content_type", content_type or "article"),
                        content_id=item.get("content_id"),
                        score=item.get("score", 0.0),
                        reason=item.get("reason", "热门内容"),
                    )
                    for item in cached_hot[:limit]
                ]

                return schemas.RecommendationResponse(
                    items=items,
                    total_count=len(items),
                    algorithm_type="hot",
                    user_id=None,
                    generated_at=datetime.utcnow(),
                )

        # 从推荐服务获取热门内容
        hot_items = await recommendation_service._hot_content_recommendation(
            db=db,
            user_id=None,  # 游客用户
            limit=limit,
            content_type=content_type,
            exclude_seen=False,
        )

        # 转换为推荐响应格式
        from app.schemas.recommendation import RecommendationItem

        items = [
            RecommendationItem(
                content_type=item.content_type,
                content_id=item.content_id,
                score=item.score,
                reason=item.reason or "热门内容",
            )
            for item in hot_items
        ]

        response = schemas.RecommendationResponse(
            items=items,
            total_count=len(items),
            algorithm_type="hot",
            user_id=None,
            generated_at=datetime.utcnow(),
        )

        # 缓存热门内容（以字典格式）
        if use_cache and items:
            hot_content_data = [
                {
                    "content_type": item.content_type,
                    "content_id": item.content_id,
                    "score": item.score,
                    "reason": item.reason,
                }
                for item in items
            ]
            await recommendation_cache_service.set_hot_content(
                content_type=content_type or "article",
                hot_content=hot_content_data,
                time_range="week",
            )

        return response

    # 登录用户的个性化推荐逻辑
    # 尝试从缓存获取推荐结果
    if use_cache:
        cached_recommendations = await recommendation_cache_service.get_user_recommendations(
            user_id=current_user.id,
            algorithm_type=algorithm_type or "hybrid",
            content_type=content_type,
            position=position,
        )

        if cached_recommendations:
            # 如果需要限制数量，截取结果
            if len(cached_recommendations.items) > limit:
                cached_recommendations.items = cached_recommendations.items[:limit]
                cached_recommendations.total_count = limit

            return cached_recommendations

    # 从推荐服务获取推荐结果
    recommendations = await recommendation_service.get_recommendations(
        db=db,
        user_id=current_user.id,
        algorithm_type=algorithm_type,
        limit=limit,
        content_type=content_type,
        position=position,
        exclude_seen=exclude_seen,
    )

    # 缓存推荐结果
    if use_cache:
        await recommendation_cache_service.set_user_recommendations(
            user_id=current_user.id,
            algorithm_type=recommendations.algorithm_type,
            recommendations=recommendations,
            content_type=content_type,
            position=position,
        )

    return recommendations


@router.get(
    "/similar/{content_type}/{content_id}",
    response_model=schemas.SimilarContentResponse,
)
async def get_similar_content(
    *,
    db: AsyncSession = Depends(deps.get_db),
    content_type: str,
    content_id: int,
    similarity_type: str = Query(
        "hybrid",
        description="相似度类型：tag_based, category_based, content_based, hybrid",
    ),
    limit: int = Query(10, ge=1, le=20, description="相似内容数量"),
    use_cache: bool = Query(True, description="是否使用缓存"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
) -> Any:
    """获取相似内容推荐（支持游客访问）"""

    # 验证内容类型
    if content_type not in ["article", "video"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的内容类型",
        )

    # 验证内容是否存在
    if content_type == "article":
        content = await crud.article.get(db, id=content_id)
        if not content or not content.is_published:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文章不存在或未发布",
            )
    elif content_type == "video":
        content = await crud.video.get(db, id=content_id)
        if not content or not content.is_published:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="视频不存在或未发布",
            )

    # 尝试从缓存获取相似内容
    if use_cache:
        cached_similar = await recommendation_cache_service.get_similar_content(
            content_type=content_type,
            content_id=content_id,
            similarity_type=similarity_type,
        )

        if cached_similar:
            return schemas.SimilarContentResponse(
                content_type=content_type,
                content_id=content_id,
                similar_items=cached_similar[:limit],
                similarity_type=similarity_type,
            )

    # 从推荐服务获取相似内容（支持游客访问）
    similar_items = await recommendation_service._get_similar_content(
        db=db,
        content_type=content_type,
        content_id=content_id,
        limit=limit,
        user_id=current_user.id if current_user else None,  # 游客用户传入None
    )

    # 缓存相似内容
    if use_cache and similar_items:
        await recommendation_cache_service.set_similar_content(
            content_type=content_type,
            content_id=content_id,
            similar_items=similar_items,
            similarity_type=similarity_type,
        )

    return schemas.SimilarContentResponse(
        content_type=content_type,
        content_id=content_id,
        similar_items=similar_items,
        similarity_type=similarity_type,
    )


@router.get("/hot/{content_type}", response_model=schemas.HotContentResponse)
async def get_hot_content(
    *,
    db: AsyncSession = Depends(deps.get_db),
    content_type: str,
    time_range: str = Query("week", description="时间范围：today, week, month"),
    limit: int = Query(20, ge=1, le=50, description="热门内容数量"),
    use_cache: bool = Query(True, description="是否使用缓存"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
) -> Any:
    """获取热门内容（完全开放，支持游客访问）"""

    # 验证内容类型
    if content_type not in ["article", "video"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的内容类型",
        )

    # 验证时间范围
    if time_range not in ["today", "week", "month"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的时间范围",
        )

    # 尝试从缓存获取热门内容
    if use_cache:
        cached_hot = await recommendation_cache_service.get_hot_content(
            content_type=content_type,
            time_range=time_range,
        )

        if cached_hot:
            return schemas.HotContentResponse(
                content_type=content_type,
                items=cached_hot[:limit],
                time_range=time_range,
                generated_at=datetime.utcnow(),
            )

    # 从推荐服务获取热门内容（支持游客访问）
    hot_items = await recommendation_service._hot_content_recommendation(
        db=db,
        user_id=current_user.id if current_user else None,  # 游客用户传入None
        limit=limit,
        content_type=content_type,
        exclude_seen=False,  # 热门内容不排除已浏览
    )

    # 转换为字典格式
    hot_content_data = [
        {
            "content_type": item.content_type,
            "content_id": item.content_id,
            "score": item.score,
            "reason": item.reason,
        }
        for item in hot_items
    ]

    # 缓存热门内容
    if use_cache and hot_content_data:
        await recommendation_cache_service.set_hot_content(
            content_type=content_type,
            hot_content=hot_content_data,
            time_range=time_range,
        )

    return schemas.HotContentResponse(
        content_type=content_type,
        items=hot_content_data,
        time_range=time_range,
        generated_at=datetime.utcnow(),
    )


@router.post("/feedback", response_model=dict)
async def submit_recommendation_feedback(
    *,
    db: AsyncSession = Depends(deps.get_db),
    feedback: schemas.RecommendationFeedback,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """提交推荐反馈"""

    # 验证推荐记录是否存在且属于当前用户
    recommendation_log = await crud.recommendation_log.get(db, id=feedback.recommendation_log_id)
    if not recommendation_log:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="推荐记录不存在",
        )

    if recommendation_log.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限操作此推荐记录",
        )

    # 如果是点击反馈，更新推荐记录的点击状态
    if feedback.feedback_type == "like" and feedback.content_id:
        await crud.recommendation_log.update_click_status(
            db=db,
            log_id=feedback.recommendation_log_id,
            content_id=feedback.content_id,
        )

    # 这里可以添加更多的反馈处理逻辑，比如：
    # 1. 记录用户反馈到数据库
    # 2. 更新用户画像
    # 3. 调整推荐算法参数
    # 4. 清除相关缓存

    # 如果用户不喜欢某个推荐，清除相关缓存
    if feedback.feedback_type in ["dislike", "not_interested"]:
        await recommendation_cache_service.invalidate_user_cache(current_user.id)

    return {"message": "反馈提交成功", "status": "success"}


@router.get("/stats", response_model=schemas.RecommendationStats)
async def get_recommendation_stats(
    *,
    db: AsyncSession = Depends(deps.get_db),
    days: int = Query(7, ge=1, le=30, description="统计天数"),
    current_user: models.User = Depends(
        PermissionDeps.require_permission_enhanced(
            PermissionObject(resource=ResourceType.SYSTEM, action=Action.READ, scope=Scope.ALL)
        )
    ),
) -> Any:
    """获取推荐统计信息（仅管理员可访问）"""
    # 获取推荐统计数据
    total_recommendations = (
        db.query(models.RecommendationLog)
        .filter(models.RecommendationLog.created_at >= datetime.utcnow() - timedelta(days=days))
        .count()
    )

    # 计算总体点击率
    overall_ctr = await crud.recommendation_log.get_click_through_rate(db, days=days)

    # 按算法类型统计性能
    algorithm_performance = {}
    for algorithm in ["collaborative", "content_based", "hot", "hybrid"]:
        ctr = await crud.recommendation_log.get_click_through_rate(
            db, algorithm_type=algorithm, days=days
        )
        algorithm_performance[algorithm] = {"click_through_rate": ctr}

    # 统计热门内容类型
    popular_content_types = {
        "article": 0,
        "video": 0,
    }

    # 这里可以添加更详细的统计逻辑

    return schemas.RecommendationStats(
        total_recommendations=total_recommendations,
        click_through_rate=overall_ctr,
        algorithm_performance=algorithm_performance,
        popular_content_types=popular_content_types,
    )


@router.get("/evaluation/report", response_model=dict)
async def get_evaluation_report(
    *,
    db: AsyncSession = Depends(deps.get_db),
    days: int = Query(7, ge=1, le=30, description="评估天数"),
    current_user: models.User = Depends(
        PermissionDeps.require_permission_enhanced(
            PermissionObject(resource=ResourceType.SYSTEM, action=Action.READ, scope=Scope.ALL)
        )
    ),
) -> Any:
    """获取推荐效果评估报告（仅管理员可访问）"""
    # 生成综合评估报告
    from app.services.recommendation_evaluation_service import (
        recommendation_evaluation_service,
    )

    report = await recommendation_evaluation_service.generate_comprehensive_report(db, days=days)

    return report


@router.get("/evaluation/algorithm/{algorithm_type}", response_model=dict)
async def get_algorithm_evaluation(
    *,
    db: AsyncSession = Depends(deps.get_db),
    algorithm_type: str,
    days: int = Query(7, ge=1, le=30, description="评估天数"),
    current_user: models.User = Depends(
        PermissionDeps.require_permission_enhanced(
            PermissionObject(resource=ResourceType.SYSTEM, action=Action.READ, scope=Scope.ALL)
        )
    ),
) -> Any:
    """获取特定算法的评估结果（仅管理员可访问）"""
    # 验证算法类型
    if algorithm_type not in ["collaborative", "content_based", "hot", "hybrid"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的算法类型",
        )

    from app.services.recommendation_evaluation_service import (
        recommendation_evaluation_service,
    )

    # 计算各项指标
    ctr_data = await recommendation_evaluation_service.calculate_click_through_rate(
        db, algorithm_type=algorithm_type, days=days
    )

    diversity_data = await recommendation_evaluation_service.calculate_diversity_score(
        db, algorithm_type=algorithm_type, days=days
    )

    coverage_data = await recommendation_evaluation_service.calculate_coverage_score(
        db, algorithm_type=algorithm_type, days=days
    )

    return {
        "algorithm_type": algorithm_type,
        "evaluation_period": f"{days} days",
        "click_through_rate": ctr_data,
        "diversity": diversity_data,
        "coverage": coverage_data,
        "generated_at": datetime.utcnow().isoformat(),
    }


@router.get("/evaluation/user/{user_id}", response_model=dict)
async def get_user_evaluation(
    *,
    db: AsyncSession = Depends(deps.get_db),
    user_id: int,
    algorithm_type: str = Query("hybrid", description="算法类型"),
    days: int = Query(7, ge=1, le=30, description="评估天数"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取特定用户的推荐效果评估（仅管理员或用户本人可访问）"""

    # 检查权限：管理员或用户本人
    is_admin = await PermissionChecker.check_permission(
        db,
        current_user,
        PermissionObject(resource=ResourceType.SYSTEM, action=Action.MANAGE, scope=Scope.ALL),
    )
    if not (is_admin or current_user.id == user_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此用户的评估数据",
        )

    from app.services.recommendation_evaluation_service import (
        recommendation_evaluation_service,
    )

    # 计算用户相关指标
    precision_recall = await recommendation_evaluation_service.calculate_precision_recall(
        db, user_id=user_id, algorithm_type=algorithm_type, days=days
    )

    novelty_data = await recommendation_evaluation_service.calculate_novelty_score(
        db, user_id=user_id, algorithm_type=algorithm_type, days=days
    )

    return {
        "user_id": user_id,
        "algorithm_type": algorithm_type,
        "evaluation_period": f"{days} days",
        "precision_recall": precision_recall,
        "novelty": novelty_data,
        "generated_at": datetime.utcnow().isoformat(),
    }
