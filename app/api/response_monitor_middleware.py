"""
响应格式监控中间件

在开发环境中监控API响应格式，确保所有响应都符合预期标准。
"""

import json
from typing import Any

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.config import settings
from app.services.logger import get_logger
from app.utils.response_validator import ResponseFormatChecker

logger = get_logger(__name__)


class ResponseMonitorMiddleware(BaseHTTPMiddleware):
    """响应格式监控中间件
    
    仅在开发环境中启用，用于监控和记录响应格式问题。
    """
    
    def __init__(self, app, enabled: bool = None):
        super().__init__(app)
        # 默认在开发环境中启用
        self.enabled = enabled if enabled is not None else (settings.ENVIRONMENT == "development")
        self.checker = ResponseFormatChecker(enabled=self.enabled)
        
        if self.enabled:
            logger.info("响应格式监控中间件已启用")
    
    async def dispatch(self, request: Request, call_next) -> Any:
        if not self.enabled:
            return await call_next(request)
        
        response = await call_next(request)
        
        # 只监控API端点
        if not request.url.path.startswith("/api/v1"):
            return response
        
        try:
            # 读取响应内容
            body = b""
            async for chunk in response.body_iterator:
                body += chunk
            
            if body:
                content = json.loads(body.decode("utf-8"))
                endpoint = f"{request.method} {request.url.path}"
                
                # 检查响应格式
                is_valid = self.checker.check_response(content, endpoint)
                
                if not is_valid:
                    # 记录格式问题
                    issues = self.checker.get_issues()
                    logger.warning(f"响应格式问题 - {endpoint}: {issues}")
                    self.checker.clear_issues()
                else:
                    # 在调试模式下记录成功的验证
                    if settings.DEBUG:
                        logger.debug(f"响应格式验证通过 - {endpoint}")
                
                # 检查特定的格式问题
                self._check_specific_issues(content, endpoint)
            
            # 重新创建响应
            from fastapi import Response
            return Response(
                content=body,
                status_code=response.status_code,
                headers=dict(response.headers),
                media_type=response.media_type
            )
            
        except Exception as e:
            logger.error(f"响应监控异常: {e}")
            return response
    
    def _check_specific_issues(self, content: dict, endpoint: str):
        """检查特定的格式问题"""
        
        # 检查双重嵌套
        if content.get("status") == "success" and isinstance(content.get("data"), dict):
            data = content["data"]
            if data.get("status") in ["success", "error"]:
                logger.warning(f"可能的双重嵌套 - {endpoint}: 内层数据包含status字段")
        
        # 检查分页响应的完整性
        if (content.get("status") == "success" and 
            isinstance(content.get("data"), dict) and 
            "items" in content["data"]):
            
            pagination_data = content["data"]
            required_pagination_fields = ["items", "has_next", "has_previous"]
            missing_fields = [field for field in required_pagination_fields 
                            if field not in pagination_data]
            
            if missing_fields:
                logger.warning(f"分页响应缺少字段 - {endpoint}: {missing_fields}")
        
        # 检查内容响应的完整性
        if (content.get("status") == "success" and 
            isinstance(content.get("data"), dict)):
            
            data = content["data"]
            
            # 如果是单个内容对象
            if "id" in data and "title" in data:
                required_content_fields = ["id", "title", "author"]
                missing_fields = [field for field in required_content_fields 
                                if field not in data]
                
                if missing_fields:
                    logger.warning(f"内容响应缺少字段 - {endpoint}: {missing_fields}")
            
            # 如果是内容列表
            elif "items" in data and isinstance(data["items"], list) and data["items"]:
                first_item = data["items"][0]
                if isinstance(first_item, dict) and "id" in first_item:
                    required_content_fields = ["id", "title", "author"]
                    missing_fields = [field for field in required_content_fields 
                                    if field not in first_item]
                    
                    if missing_fields:
                        logger.warning(f"内容列表项缺少字段 - {endpoint}: {missing_fields}")
        
        # 检查错误响应的完整性
        if content.get("status") == "error":
            if "message" not in content:
                logger.warning(f"错误响应缺少message字段 - {endpoint}")
    
    def get_format_summary(self) -> dict:
        """获取格式检查摘要"""
        return {
            "enabled": self.enabled,
            "total_issues": len(self.checker.get_issues()),
            "issues": self.checker.get_issues()
        }


# 创建全局实例
response_monitor = ResponseMonitorMiddleware(None, enabled=settings.ENVIRONMENT == "development")
