"""游标分页实现

使用 fastapi-pagination 库实现游标分页，提供更好的性能和用户体验。
游标分页特别适合大数据集和实时数据更新的场景。
"""

from typing import Any, Generic, TypeVar

from fastapi_pagination import add_pagination, set_page
from fastapi_pagination.cursor import CursorPage, CursorParams
from fastapi_pagination.ext.sqlalchemy import paginate
from pydantic import BaseModel, Field
from sqlalchemy import asc, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession

T = TypeVar("T")


class CursorPaginationParams(BaseModel):
    """游标分页参数"""

    cursor: str | None = Field(None, description="游标位置，用于获取下一页数据")
    size: int = Field(20, ge=1, le=100, description="每页大小，范围1-100")
    order_by: str = Field("id", description="排序字段")
    order_direction: str = Field("desc", pattern="^(asc|desc)$", description="排序方向：asc或desc")


class CursorPaginationResponse(BaseModel, Generic[T]):
    """游标分页响应"""

    items: list[T] = Field(..., description="数据列表")
    has_next: bool = Field(..., description="是否有下一页")
    has_previous: bool = Field(..., description="是否有上一页")
    next_cursor: str | None = Field(None, description="下一页游标")
    previous_cursor: str | None = Field(None, description="上一页游标")
    total_count: int | None = Field(None, description="总数量（可选，计算成本较高）")


class CursorPaginator:
    """游标分页器"""

    @staticmethod
    async def paginate_query(
        db: AsyncSession,
        query: Any,
        params: CursorPaginationParams,
        cursor_field: str = "id",
        include_total: bool = False,
    ) -> CursorPaginationResponse:
        """对查询进行游标分页

        Args:
            db: 数据库会话
            query: SQLAlchemy查询对象
            params: 分页参数
            cursor_field: 用作游标的字段名
            include_total: 是否包含总数量（会增加查询成本）

        Returns:
            分页响应
        """
        # 确定排序方向
        order_func = desc if params.order_direction == "desc" else asc

        # 获取模型类（假设查询的是单个模型）
        model_class = query.column_descriptions[0]["type"]
        cursor_column = getattr(model_class, cursor_field)

        # 应用排序
        ordered_query = query.order_by(order_func(cursor_column))

        # 应用游标过滤
        if params.cursor:
            try:
                cursor_value = int(params.cursor)  # 假设游标是整数ID
                if params.order_direction == "desc":
                    ordered_query = ordered_query.where(cursor_column < cursor_value)
                else:
                    ordered_query = ordered_query.where(cursor_column > cursor_value)
            except ValueError:
                # 如果游标不是有效的整数，忽略游标过滤
                pass

        # 获取比请求数量多1个的记录，用于判断是否有下一页
        limit_query = ordered_query.limit(params.size + 1)

        # 执行查询
        result = await db.execute(limit_query)
        items = result.scalars().all()

        # 判断是否有下一页
        has_next = len(items) > params.size
        if has_next:
            items = items[:-1]  # 移除多获取的那一个记录

        # 计算游标
        next_cursor = None
        previous_cursor = None

        if items:
            if has_next:
                next_cursor = str(getattr(items[-1], cursor_field))

            # 对于上一页游标，我们需要反向查询
            if params.cursor:
                previous_cursor = await CursorPaginator._get_previous_cursor(
                    db, query, params, cursor_field, items[0]
                )

        # 计算总数量（可选）
        total_count = None
        if include_total:
            count_query = select(func.count()).select_from(query.subquery())
            count_result = await db.execute(count_query)
            total_count = count_result.scalar()

        return CursorPaginationResponse(
            items=items,
            has_next=has_next,
            has_previous=bool(params.cursor),
            next_cursor=next_cursor,
            previous_cursor=previous_cursor,
            total_count=total_count,
        )

    @staticmethod
    async def _get_previous_cursor(
        db: AsyncSession,
        base_query: Any,
        params: CursorPaginationParams,
        cursor_field: str,
        first_item: Any,
    ) -> str | None:
        """获取上一页游标"""
        model_class = base_query.column_descriptions[0]["type"]
        cursor_column = getattr(model_class, cursor_field)
        first_item_cursor = getattr(first_item, cursor_field)

        # 反向查询以获取上一页的最后一个元素
        reverse_order_func = asc if params.order_direction == "desc" else desc

        if params.order_direction == "desc":
            reverse_query = base_query.where(cursor_column > first_item_cursor)
        else:
            reverse_query = base_query.where(cursor_column < first_item_cursor)

        reverse_query = reverse_query.order_by(reverse_order_func(cursor_column)).limit(params.size)

        result = await db.execute(reverse_query)
        reverse_items = result.scalars().all()

        if reverse_items:
            return str(getattr(reverse_items[-1], cursor_field))

        return None


class FastAPIPaginationIntegration:
    """FastAPI Pagination 库集成"""

    @staticmethod
    def setup_pagination(app):
        """设置 FastAPI Pagination"""
        add_pagination(app)
        # 设置默认的游标分页
        set_page(CursorPage[Any])

    @staticmethod
    async def paginate_sqlalchemy_query(
        db: AsyncSession, query: Any, params: CursorParams | None = None
    ) -> CursorPage:
        """使用 fastapi-pagination 对 SQLAlchemy 查询进行游标分页"""
        return await paginate(db, query, params)


# 兼容性适配器，用于现有代码的平滑迁移
class PaginationAdapter:
    """分页适配器，提供从传统分页到游标分页的迁移支持"""

    @staticmethod
    def convert_offset_to_cursor(
        offset: int, limit: int, total: int, items: list[Any], cursor_field: str = "id"
    ) -> CursorPaginationResponse:
        """将传统的 offset/limit 分页结果转换为游标分页格式"""
        has_next = (offset + limit) < total
        has_previous = offset > 0

        next_cursor = None
        previous_cursor = None

        if items:
            if has_next:
                next_cursor = str(getattr(items[-1], cursor_field))
            if has_previous and len(items) > 0:
                previous_cursor = str(getattr(items[0], cursor_field))

        return CursorPaginationResponse(
            items=items,
            has_next=has_next,
            has_previous=has_previous,
            next_cursor=next_cursor,
            previous_cursor=previous_cursor,
            total_count=total,
        )

    @staticmethod
    def cursor_params_to_offset(
        cursor: str | None, size: int, model_class: Any, cursor_field: str = "id"
    ) -> tuple[int, int]:
        """将游标参数转换为 offset/limit 参数（用于向后兼容）"""
        if not cursor:
            return 0, size

        try:
            cursor_value = int(cursor)
            # 这里需要根据具体业务逻辑计算 offset
            # 简化实现，实际使用中可能需要更复杂的逻辑
            offset = 0  # 游标分页通常不需要 offset
            return offset, size
        except ValueError:
            return 0, size
