"""
响应格式验证工具

用于验证API响应格式的一致性，确保所有响应都符合预期的标准格式。
"""

import json
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, ValidationError

from app.core.pagination import CursorPaginationResponse
from app.schemas.simple_unified_response import UnifiedContentResponse
from app.services.logger import get_logger

logger = get_logger(__name__)


class ResponseFormatError(Exception):
    """响应格式错误"""
    pass


class StandardSuccessResponse(BaseModel):
    """标准成功响应格式"""
    status: str = "success"
    data: Any


class StandardErrorResponse(BaseModel):
    """标准错误响应格式"""
    status: str = "error"
    message: str
    status_code: Optional[int] = None
    detail: Optional[str] = None
    errors: Optional[List[Dict[str, Any]]] = None


class ResponseValidator:
    """响应格式验证器"""
    
    @staticmethod
    def validate_success_response(response_data: Dict[str, Any]) -> bool:
        """验证成功响应格式"""
        try:
            StandardSuccessResponse(**response_data)
            return True
        except ValidationError as e:
            logger.warning(f"成功响应格式验证失败: {e}")
            return False
    
    @staticmethod
    def validate_error_response(response_data: Dict[str, Any]) -> bool:
        """验证错误响应格式"""
        try:
            StandardErrorResponse(**response_data)
            return True
        except ValidationError as e:
            logger.warning(f"错误响应格式验证失败: {e}")
            return False
    
    @staticmethod
    def validate_pagination_response(response_data: Dict[str, Any]) -> bool:
        """验证分页响应格式"""
        if not ResponseValidator.validate_success_response(response_data):
            return False
        
        try:
            # 检查data字段是否符合分页响应格式
            data = response_data.get("data", {})
            required_fields = {"items", "has_next", "has_previous"}
            
            if not all(field in data for field in required_fields):
                logger.warning(f"分页响应缺少必需字段: {required_fields - set(data.keys())}")
                return False
            
            # 验证items是否为列表
            if not isinstance(data.get("items"), list):
                logger.warning("分页响应的items字段必须是列表")
                return False
            
            return True
        except Exception as e:
            logger.warning(f"分页响应格式验证失败: {e}")
            return False
    
    @staticmethod
    def validate_content_response(response_data: Dict[str, Any]) -> bool:
        """验证内容响应格式"""
        if not ResponseValidator.validate_success_response(response_data):
            return False
        
        try:
            data = response_data.get("data", {})
            
            # 检查是否为分页响应
            if "items" in data and "has_next" in data:
                # 验证分页响应中的内容项
                items = data.get("items", [])
                if items and isinstance(items, list):
                    # 验证第一个内容项的格式
                    first_item = items[0]
                    UnifiedContentResponse(**first_item)
                return True
            else:
                # 验证单个内容响应
                UnifiedContentResponse(**data)
                return True
        except ValidationError as e:
            logger.warning(f"内容响应格式验证失败: {e}")
            return False
        except Exception as e:
            logger.warning(f"内容响应格式验证异常: {e}")
            return False
    
    @staticmethod
    def validate_response_consistency(responses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证多个响应的一致性"""
        results = {
            "total_responses": len(responses),
            "valid_responses": 0,
            "invalid_responses": 0,
            "format_issues": [],
            "consistency_issues": []
        }
        
        success_format = None
        error_format = None
        
        for i, response in enumerate(responses):
            try:
                if response.get("status") == "success":
                    if ResponseValidator.validate_success_response(response):
                        results["valid_responses"] += 1
                        if success_format is None:
                            success_format = set(response.keys())
                        elif set(response.keys()) != success_format:
                            results["consistency_issues"].append(
                                f"响应 {i}: 成功响应字段不一致"
                            )
                    else:
                        results["invalid_responses"] += 1
                        results["format_issues"].append(f"响应 {i}: 成功响应格式无效")
                
                elif response.get("status") == "error":
                    if ResponseValidator.validate_error_response(response):
                        results["valid_responses"] += 1
                        if error_format is None:
                            error_format = set(response.keys())
                        elif set(response.keys()) != error_format:
                            results["consistency_issues"].append(
                                f"响应 {i}: 错误响应字段不一致"
                            )
                    else:
                        results["invalid_responses"] += 1
                        results["format_issues"].append(f"响应 {i}: 错误响应格式无效")
                
                else:
                    results["invalid_responses"] += 1
                    results["format_issues"].append(f"响应 {i}: 未知响应状态")
                    
            except Exception as e:
                results["invalid_responses"] += 1
                results["format_issues"].append(f"响应 {i}: 验证异常 - {str(e)}")
        
        return results


class ResponseFormatChecker:
    """响应格式检查器 - 用于开发和测试环境"""
    
    def __init__(self, enabled: bool = True):
        self.enabled = enabled
        self.issues_found = []
    
    def check_response(self, response_data: Any, endpoint: str = "unknown") -> bool:
        """检查单个响应格式"""
        if not self.enabled:
            return True
        
        try:
            if isinstance(response_data, str):
                response_data = json.loads(response_data)
            
            if not isinstance(response_data, dict):
                self.issues_found.append(f"{endpoint}: 响应不是字典格式")
                return False
            
            # 检查基本格式
            if "status" not in response_data:
                self.issues_found.append(f"{endpoint}: 缺少status字段")
                return False
            
            status = response_data.get("status")
            if status == "success":
                if not ResponseValidator.validate_success_response(response_data):
                    self.issues_found.append(f"{endpoint}: 成功响应格式无效")
                    return False
            elif status == "error":
                if not ResponseValidator.validate_error_response(response_data):
                    self.issues_found.append(f"{endpoint}: 错误响应格式无效")
                    return False
            else:
                self.issues_found.append(f"{endpoint}: 未知状态值: {status}")
                return False
            
            return True
            
        except Exception as e:
            self.issues_found.append(f"{endpoint}: 检查异常 - {str(e)}")
            return False
    
    def get_issues(self) -> List[str]:
        """获取发现的问题列表"""
        return self.issues_found.copy()
    
    def clear_issues(self):
        """清除问题列表"""
        self.issues_found.clear()
    
    def has_issues(self) -> bool:
        """是否有问题"""
        return len(self.issues_found) > 0


# 全局响应格式检查器实例
response_checker = ResponseFormatChecker(enabled=True)
