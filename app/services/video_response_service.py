from typing import Any

from sqlalchemy.ext.asyncio import AsyncSession

from app.models.video import Video
from app.schemas.unified_response import ContentResponseOptions
from app.services.unified_response_service import UnifiedResponseService


class VideoResponseService(UnifiedResponseService):
    """
    视频响应服务
    继承自 UnifiedResponseService，提供视频特有的响应构建逻辑
    """

    async def build_video_response(
        self,
        db: AsyncSession,
        video: Video,
        options: ContentResponseOptions,
        current_user_id: int | None = None,
    ) -> dict[str, Any]:
        """
        构建单个视频的响应
        """
        return await self.build_content_response(
            db=db,
            content=video,
            content_type="video",
            options=options,
            current_user_id=current_user_id,
        )

    async def build_video_list_response(
        self,
        db: AsyncSession,
        videos: list[Video],
        options: ContentResponseOptions,
        current_user_id: int | None = None,
    ) -> list[dict[str, Any]]:
        """
        构建视频列表的响应
        """
        return await self.build_content_list_response(
            db=db,
            contents=videos,
            content_type="video",
            options=options,
            current_user_id=current_user_id,
        )


# 创建全局实例
video_response_service = VideoResponseService()
