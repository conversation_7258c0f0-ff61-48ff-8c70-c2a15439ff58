from datetime import datetime
from typing import Any

from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app import crud, models
from app.core.pagination import (
    CursorPaginationParams,
    CursorPaginationResponse,
    CursorPaginator,
)
from app.crud.base import CRUDBase
from app.models import ReviewStatus
from app.models.article import Article
from app.models.review import ContentType, Review
from app.models.user import User
from app.schemas.article import ArticleCreate, ArticleStatus, ArticleUpdate
from app.services.history_cache_service import history_service


class CRUDArticle(CRUDBase[Article, ArticleCreate, ArticleUpdate]):
    async def get_hot_articles_by_category(
        self,
        db: AsyncSession,
        *,
        category_ids: list[int],
        time_threshold: datetime,
        skip: int = 0,
        limit: int = 100,
    ) -> list[Article]:
        """获取分类下的热门文章"""
        # 构建查询：计算文章热度分数
        stmt = (
            select(
                self.model.id,
                (
                    func.coalesce(func.count(models.Like.id), 0) * 3
                    + func.coalesce(func.count(models.Favorite.id), 0) * 5
                    + func.coalesce(func.count(models.Comment.id), 0) * 4
                ).label("hot_score"),
            )
            .outerjoin(
                models.Like,
                and_(
                    models.Like.content_type == "article",
                    models.Like.content_id == self.model.id,
                    models.Like.is_active,
                    models.Like.created_at >= time_threshold,
                ),
            )
            .outerjoin(
                models.Favorite,
                and_(
                    models.Favorite.content_type == "article",
                    models.Favorite.content_id == self.model.id,
                    models.Favorite.is_active,
                    models.Favorite.created_at >= time_threshold,
                ),
            )
            .outerjoin(
                models.Comment,
                and_(
                    models.Comment.comment_type == "article",
                    models.Comment.article_id == self.model.id,
                    models.Comment.is_visible,
                    models.Comment.created_at >= time_threshold,
                ),
            )
            .where(
                self.model.is_published,
                self.model.is_approved,
            )
        )
        if category_ids:
            stmt = stmt.where(self.model.category_id.in_(category_ids))

        stmt = stmt.group_by(self.model.id).order_by(desc("hot_score")).offset(skip).limit(limit)

        result = await db.execute(stmt)
        hot_article_ids = [row[0] for row in result.all()]

        if not hot_article_ids:
            return []

        # 获取文章对象
        query = (
            select(self.model)
            .where(self.model.id.in_(hot_article_ids))
            .options(
                selectinload(self.model.author),
                selectinload(self.model.tags),
                selectinload(self.model.category),
            )
        )

        articles_map = {
            article.id: article for article in (await db.execute(query)).scalars().all()
        }

        # 按照热度顺序返回
        sorted_articles = [articles_map[id] for id in hot_article_ids if id in articles_map]

        return sorted_articles

    async def get_paginated_articles_by_user(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        params: CursorPaginationParams,
        status_filter: ArticleStatus,
        is_own_articles: bool,
        include_review: bool,
        include_total: bool,
    ) -> CursorPaginationResponse:
        """
        获取指定用户的文章列表（统一接口-重构版）
        - 使用 selectinload 解决 N+1 问题
        - 使用 EXISTS 子查询代替 JOIN
        - 将复杂查询逻辑从 API endpoint 移至 CRUD 层
        """
        # 基础查询，并预加载所需关联数据
        query = select(self.model).options(
            selectinload(self.model.author),
            selectinload(self.model.tags),
            selectinload(self.model.category),
        )

        # 如果需要审核信息，也预加载
        if include_review and is_own_articles:
            query = query.options(
                selectinload(self.model.reviews).selectinload(models.Review.reviewer)
            )

        # 应用基础的用户筛选
        query = query.where(self.model.author_id == user_id)

        # 权限检查和状态筛选
        if not is_own_articles:
            # 查看其他用户的文章，只能看已发布且审核通过的
            query = query.where(self.model.is_published == True, self.model.is_approved == True)
        else:
            # 作者本人可以根据status参数筛选
            if status_filter == ArticleStatus.DRAFT:
                query = query.where(self.model.is_published == False)
            elif status_filter == ArticleStatus.PUBLISHED_APPROVED:
                query = query.where(self.model.is_published == True, self.model.is_approved == True)
            elif status_filter in [
                ArticleStatus.PUBLISHED_PENDING,
                ArticleStatus.PUBLISHED_REJECTED,
            ]:
                # 使用 EXISTS 子查询来判断审核状态，比 JOIN 更高效
                review_status_filter = (
                    ReviewStatus.PENDING
                    if status_filter == ArticleStatus.PUBLISHED_PENDING
                    else ReviewStatus.REJECTED
                )
                subquery = (
                    select(Review.id)
                    .where(
                        Review.content_id == self.model.id,
                        Review.content_type == ContentType.ARTICLE,
                        Review.status == review_status_filter,
                    )
                    .exists()
                )
                query = query.where(self.model.is_published == True, subquery)
            # "all" 状态不需要额外的where条件

        # 使用游标分页器
        paginated_result = await CursorPaginator.paginate_query(
            db=db,
            query=query,
            params=params,
            cursor_field=params.order_by,
            include_total=include_total,
        )

        return paginated_result

    async def get_by_title(self, db: AsyncSession, *, title: str) -> Article | None:
        """根据标题获取文章"""
        result = await db.execute(select(self.model).where(self.model.title == title))
        return result.scalar_one_or_none()

    async def get_multi_by_author(
        self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[Article]:
        """获取指定作者的文章列表"""
        result = await db.execute(
            select(self.model)
            .options(
                selectinload(self.model.tags),
                selectinload(self.model.author),
            )
            .where(self.model.author_id == author_id)
            .offset(skip)
            .limit(limit)
        )

        return result.scalars().all()

    async def get_multi_by_ids(
        self, db: AsyncSession, *, ids: list[int]
    ) -> list[Article]:
        """根据ID列表批量获取文章
        
        Args:
            db: 数据库会话
            ids: 文章ID列表
            
        Returns:
            文章列表，按照传入的ID顺序排序
        """
        if not ids:
            return []
            
        result = await db.execute(
            select(self.model)
            .options(
                selectinload(self.model.tags),
                selectinload(self.model.author),
                selectinload(self.model.category),
                selectinload(self.model.reviews),  # 预加载审核信息
            )
            .where(self.model.id.in_(ids))
        )
        
        articles = result.scalars().all()
        
        # 按照传入的ID顺序重新排序
        articles_dict = {article.id: article for article in articles}
        ordered_articles = [articles_dict[id] for id in ids if id in articles_dict]
        
        return ordered_articles

    async def get_published(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> list[Article]:
        """获取已发布的文章列表"""
        result = await db.execute(
            select(self.model)
            .options(
                selectinload(self.model.tags),
            )
            .where(self.model.is_published)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def update_publish_status(
        self, db: AsyncSession, *, db_obj: Article, is_published: bool
    ) -> Article:
        """更新文章发布状态"""
        db_obj.is_published = is_published
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def get_multi_with_filters(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100, **filters
    ) -> list[Article]:
        """根据过滤条件获取文章列表

        Args:
            db: 数据库会话
            skip: 跳过的记录数
            limit: 返回的最大记录数
            **filters: 过滤条件，支持的字段：
                - author_id: 作者ID
                - category_id: 分类ID
                - is_published: 是否发布
                - is_approved: 是否审核通过
                - title: 标题（模糊匹配）

        Returns:
            文章列表
        """
        query = select(self.model).options(
            selectinload(self.model.tags),
            selectinload(self.model.author),
            selectinload(self.model.category),
        )

        # 应用过滤条件
        for field, value in filters.items():
            if hasattr(self.model, field) and value is not None:
                if field == "title":
                    # 标题模糊匹配
                    query = query.where(self.model.title.contains(value))
                else:
                    # 精确匹配
                    query = query.where(getattr(self.model, field) == value)

        # 排序、分页
        query = query.order_by(self.model.updated_at.desc()).offset(skip).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    async def count_with_filters(self, db: AsyncSession, **filters) -> int:
        """根据过滤条件统计文章数量

        Args:
            db: 数据库会话
            **filters: 过滤条件，与get_multi_with_filters相同

        Returns:
            符合条件的文章数量
        """
        query = select(func.count()).select_from(self.model)

        # 应用过滤条件
        for field, value in filters.items():
            if hasattr(self.model, field) and value is not None:
                if field == "title":
                    # 标题模糊匹配
                    query = query.where(self.model.title.contains(value))
                else:
                    # 精确匹配
                    query = query.where(getattr(self.model, field) == value)

        result = await db.execute(query)
        return result.scalar()

    async def get_paginated_articles(
        self,
        db: AsyncSession,
        *,
        params: CursorPaginationParams,
        filters: dict[str, Any],
        current_user: User | None,
        include_review: bool,
        include_total: bool,
    ) -> CursorPaginationResponse:
        """
        获取文章列表（通用分页接口-重构版）
        - 使用 selectinload 解决 N+1 问题
        - 动态处理筛选条件
        - 支持按点赞/收藏/历史记录筛选
        """
        status_filter = filters.get("status")

        # --- 新增：处理历史记录 ---
        if status_filter == "history":
            if not current_user:
                return CursorPaginationResponse(items=[], has_next=False, has_previous=False)

            # 历史记录的分页使用简单的 skip/limit，cursor 代表 skip
            try:
                skip = int(params.cursor) if params.cursor else 0
            except (ValueError, TypeError):
                skip = 0
            limit = params.size

            # 从 Redis 缓存获取历史记录ID
            article_ids = await history_service.get_history(
                user_id=current_user.id, content_type="article", skip=skip, limit=limit + 1
            )

            if not article_ids:
                return CursorPaginationResponse(items=[], has_next=False, has_previous=skip > 0)

            # 判断是否有下一页
            has_next = len(article_ids) > limit
            if has_next:
                article_ids = article_ids[:limit]  # 移除用于判断下一页的多余项

            # 从数据库获取文章对象
            query = (
                select(self.model)
                .where(self.model.id.in_(article_ids))
                .options(
                    selectinload(self.model.author),
                    selectinload(self.model.tags),
                    selectinload(self.model.category),
                    selectinload(self.model.reviews),
                )
            )
            result = await db.execute(query)
            articles_map = {article.id: article for article in result.scalars().all()}

            # 按照 Redis 返回的顺序重新排序
            sorted_articles = [articles_map[id] for id in article_ids if id in articles_map]

            # 手动构建分页结果
            return CursorPaginationResponse(
                items=sorted_articles,
                has_next=has_next,
                has_previous=skip > 0,
                next_cursor=str(skip + limit) if has_next else None,
                previous_cursor=str(skip - limit) if skip > 0 else None,
                total_count=None,  # 历史记录通常不计算总数
            )
        # --- 历史记录处理结束 ---

        # 基础查询，并预加载所需关联数据
        query = select(self.model).options(
            selectinload(self.model.author),
            selectinload(self.model.tags),
            selectinload(self.model.category),
            selectinload(self.model.reviews),  # 预加载审核信息
        )

        # 处理点赞和收藏列表
        if status_filter in [ArticleStatus.LIKES.value, ArticleStatus.FAVORITES.value]:
            if not current_user:
                # 未登录用户不能查看点赞/收藏列表，返回空
                return CursorPaginationResponse(items=[], has_next=False, has_previous=False)
            else:
                from app.services.favorite_cache_service import favorite_cache_service
                from app.services.like_cache_service import like_cache_service

                article_ids = []
                if status_filter == ArticleStatus.LIKES.value:
                    article_ids = await like_cache_service.get_user_liked_contents(
                        current_user.id, "article"
                    ) or await crud.like.get_user_liked_content_ids(
                        db, user_id=current_user.id, content_type="article"
                    )
                elif status_filter == ArticleStatus.FAVORITES.value:
                    article_ids = await favorite_cache_service.get_user_favorited_contents(
                        current_user.id, "article"
                    ) or await crud.favorite.get_user_favorite_content_ids(
                        db, user_id=current_user.id, content_type="article"
                    )

                if not article_ids:
                    return CursorPaginationResponse(items=[], has_next=False, has_previous=False)
                else:
                    query = query.where(self.model.id.in_(article_ids))
        else:
            # 原有的筛选逻辑
            if "category_ids" in filters and filters["category_ids"]:
                query = query.where(self.model.category_id.in_(filters["category_ids"]))
            elif "category_id" in filters and filters["category_id"]:
                query = query.where(self.model.category_id == filters["category_id"])

            if "author_id" in filters and filters["author_id"]:
                query = query.where(self.model.author_id == filters["author_id"])

            if status_filter == "draft":
                query = query.where(self.model.is_published == False)
            elif status_filter == "published":
                query = query.where(self.model.is_published == True, self.model.is_approved == True)

            # 权限检查：如果查询其他用户的文章，强制只能看发布的
            is_own_articles = current_user and current_user.id == filters.get("author_id")
            if filters.get("author_id") and not is_own_articles:
                query = query.where(self.model.is_published == True, self.model.is_approved == True)

        # 使用游标分页器
        paginated_result = await CursorPaginator.paginate_query(
            db=db,
            query=query,
            params=params,
            cursor_field=params.order_by,
            include_total=include_total,
        )

        return paginated_result


article = CRUDArticle(Article)
