from typing import Any

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app import crud, models
from app.core.pagination import (
    CursorPaginationParams,
    CursorPaginationResponse,
    CursorPaginator,
)
from app.crud.base import CRUDBase
from app.models import ReviewStatus
from app.models.review import ContentType, Review
from app.models.user import User
from app.models.video import Video
from app.schemas.video import VideoCreate, VideoStatus, VideoUpdate
from app.services.folder_stats_service import folder_stats_service
from app.services.history_cache_service import history_service


class CRUDVideo(CRUDBase[Video, VideoCreate, VideoUpdate]):
    async def get_by_title(self, db: AsyncSession, *, title: str) -> Video | None:
        """根据标题获取视频"""
        result = await db.execute(select(self.model).where(self.model.title == title))
        return result.scalar_one_or_none()

    async def get_multi_by_author(
        self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[Video]:
        """获取指定作者的视频列表"""
        result = await db.execute(
            select(self.model).where(self.model.author_id == author_id).offset(skip).limit(limit)
        )
        return result.scalars().all()

    async def get_published(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> list[Video]:
        """获取已发布的视频列表"""
        result = await db.execute(
            select(self.model).where(self.model.is_published).offset(skip).limit(limit)
        )
        return result.scalars().all()

    async def get_by_folder(
        self, db: AsyncSession, *, folder_id: int, skip: int = 0, limit: int = 100
    ) -> list[Video]:
        """获取指定文件夹下的视频列表"""
        result = await db.execute(
            select(self.model).where(self.model.folder_id == folder_id).offset(skip).limit(limit)
        )
        return result.scalars().all()

    async def get_multi_by_ids(self, db: AsyncSession, *, ids: list[int]) -> list[Video]:
        """根据ID列表获取多个视频"""
        if not ids:
            return []
        result = await db.execute(
            select(self.model).where(
                self.model.id.in_(ids),
                self.model.is_published == True,
                self.model.is_approved == True,
                self.model.is_deleted == False,
            )
        )
        return result.scalars().all()

    async def update_publish_status(
        self, db: AsyncSession, *, db_obj: Video, is_published: bool
    ) -> Video:
        """更新视频发布状态"""
        db_obj.is_published = is_published
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def get_pending_review(
        self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[Video]:
        """获取指定作者的待审核视频列表

        Args:
            db: 数据库会话
            author_id: 作者ID
            skip: 跳过的记录数
            limit: 返回的最大记录数

        Returns:
            待审核视频列表
        """
        result = await db.execute(
            select(self.model)
            .where(self.model.author_id == author_id)
            .where(self.model.is_published & ~self.model.is_approved)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def get_drafts(
        self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[Video]:
        """获取指定作者的草稿列表

        Args:
            db: 数据库会话
            author_id: 作者ID
            skip: 跳过的记录数
            limit: 返回的最大记录数

        Returns:
            草稿列表
        """
        result = await db.execute(
            select(self.model)
            .where(self.model.author_id == author_id)
            .where(~self.model.is_published)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def create(self, db: AsyncSession, *, obj_in: VideoCreate) -> Video:
        """创建视频，并更新文件夹视频数量

        Args:
            db: 数据库会话
            obj_in: 创建视频的数据

        Returns:
            创建的视频
        """
        # 调用父类的创建方法
        db_obj = await super().create(db, obj_in=obj_in)

        # 更新文件夹视频数量
        if db_obj.folder_id:
            await folder_stats_service.update_folder_video_count(db, db_obj.folder_id)

        return db_obj

    async def update(
        self,
        db: AsyncSession,
        *,
        db_obj: Video,
        obj_in: VideoUpdate | dict[str, Any],
    ) -> Video:
        """更新视频，如果文件夹变更则更新相关文件夹的视频数量

        Args:
            db: 数据库会话
            db_obj: 数据库中的视频对象
            obj_in: 更新的数据

        Returns:
            更新后的视频
        """
        # 记录原始文件夹ID
        old_folder_id = db_obj.folder_id

        # 调用父类的更新方法
        updated_obj = await super().update(db, db_obj=db_obj, obj_in=obj_in)

        # 如果文件夹ID变更，更新两个文件夹的视频数量
        if (
            isinstance(obj_in, dict)
            and "folder_id" in obj_in
            and old_folder_id != updated_obj.folder_id
            or hasattr(obj_in, "folder_id")
            and obj_in.folder_id is not None
            and old_folder_id != updated_obj.folder_id
        ):
            if old_folder_id:
                await folder_stats_service.update_folder_video_count(db, old_folder_id)
            if updated_obj.folder_id:
                await folder_stats_service.update_folder_video_count(db, updated_obj.folder_id)

        return updated_obj

    async def remove(self, db: AsyncSession, *, id: int) -> Video:
        """删除视频，并更新文件夹视频数量

        Args:
            db: 数据库会话
            id: 视频ID

        Returns:
            删除的视频
        """
        # 先获取视频对象，记录文件夹ID
        result = await db.execute(select(self.model).where(self.model.id == id))
        obj = result.scalar_one_or_none()
        if not obj:
            raise ValueError(f"视频ID {id} 不存在")

        folder_id = obj.folder_id

        # 调用父类的删除方法
        deleted_obj = await super().remove(db, id=id)

        # 更新文件夹视频数量
        if folder_id:
            await folder_stats_service.update_folder_video_count(db, folder_id)

        return deleted_obj

    async def move_to_folder(
        self, db: AsyncSession, *, db_obj: Video, folder_id: int | None
    ) -> Video:
        """移动视频到指定文件夹，并更新相关文件夹的视频数量

        Args:
            db: 数据库会话
            db_obj: 视频对象
            folder_id: 目标文件夹ID

        Returns:
            更新后的视频
        """
        # 记录原始文件夹ID
        old_folder_id = db_obj.folder_id

        # 更新文件夹ID
        db_obj.folder_id = folder_id
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)

        # 更新两个文件夹的视频数量
        if old_folder_id:
            await folder_stats_service.update_folder_video_count(db, old_folder_id)
        if folder_id:
            await folder_stats_service.update_folder_video_count(db, folder_id)

        return db_obj

    async def get_paginated_videos(
        self,
        db: AsyncSession,
        *,
        params: CursorPaginationParams,
        filters: dict[str, Any],
        current_user: User | None,
        include_review: bool,
        include_total: bool,
    ) -> CursorPaginationResponse:
        """
        获取视频列表（通用分页接口-重构版）
        - 使用 selectinload 解决 N+1 问题
        - 动态处理筛选条件
        - 支持按点赞/收藏/历史记录筛选
        - 支持按文件夹筛选
        """
        status_filter = filters.get("status")

        # --- 处理历史记录 ---
        if status_filter == "history":
            if not current_user:
                return CursorPaginationResponse(items=[], has_next=False, has_previous=False)

            # 历史记录的分页使用简单的 skip/limit，cursor 代表 skip
            try:
                skip = int(params.cursor) if params.cursor else 0
            except (ValueError, TypeError):
                skip = 0
            limit = params.size

            # 从 Redis 缓存获取历史记录ID
            video_ids = await history_service.get_history(
                user_id=current_user.id, content_type="video", skip=skip, limit=limit + 1
            )

            if not video_ids:
                return CursorPaginationResponse(items=[], has_next=False, has_previous=skip > 0)

            # 判断是否有下一页
            has_next = len(video_ids) > limit
            if has_next:
                video_ids = video_ids[:limit]  # 移除用于判断下一页的多余项

            # 从数据库获取视频对象
            query = (
                select(self.model)
                .where(self.model.id.in_(video_ids))
                .options(
                    selectinload(self.model.author),
                    selectinload(self.model.tags),
                    selectinload(self.model.category),
                    selectinload(self.model.folder),
                    selectinload(self.model.reviews),
                )
            )
            result = await db.execute(query)
            videos_map = {video.id: video for video in result.scalars().all()}

            # 按照 Redis 返回的顺序重新排序
            sorted_videos = [videos_map[id] for id in video_ids if id in videos_map]

            # 手动构建分页结果
            return CursorPaginationResponse(
                items=sorted_videos,
                has_next=has_next,
                has_previous=skip > 0,
                next_cursor=str(skip + limit) if has_next else None,
                previous_cursor=str(skip - limit) if skip > 0 else None,
                total_count=None,  # 历史记录通常不计算总数
            )
        # --- 历史记录处理结束 ---

        # 基础查询，并预加载所需关联数据
        query = select(self.model).options(
            selectinload(self.model.author),
            selectinload(self.model.tags),
            selectinload(self.model.category),
            selectinload(self.model.folder),
            selectinload(self.model.reviews),  # 预加载审核信息
        )

        # 处理点赞和收藏列表
        if status_filter in [VideoStatus.LIKES.value, VideoStatus.FAVORITES.value]:
            if not current_user:
                # 未登录用户不能查看点赞/收藏列表，返回空
                return CursorPaginationResponse(items=[], has_next=False, has_previous=False)
            else:
                from app.services.favorite_cache_service import favorite_cache_service
                from app.services.like_cache_service import like_cache_service

                video_ids = []
                if status_filter == VideoStatus.LIKES.value:
                    video_ids = await like_cache_service.get_user_liked_contents(
                        current_user.id, "video"
                    ) or await crud.like.get_user_liked_content_ids(
                        db, user_id=current_user.id, content_type="video"
                    )
                elif status_filter == VideoStatus.FAVORITES.value:
                    video_ids = await favorite_cache_service.get_user_favorited_contents(
                        current_user.id, "video"
                    ) or await crud.favorite.get_user_favorite_content_ids(
                        db, user_id=current_user.id, content_type="video"
                    )

                if not video_ids:
                    return CursorPaginationResponse(items=[], has_next=False, has_previous=False)
                else:
                    query = query.where(self.model.id.in_(video_ids))
        else:
            # 原有的筛选逻辑
            if "category_ids" in filters and filters["category_ids"]:
                query = query.where(self.model.category_id.in_(filters["category_ids"]))
            elif "category_id" in filters and filters["category_id"]:
                query = query.where(self.model.category_id == filters["category_id"])

            if "author_id" in filters and filters["author_id"]:
                query = query.where(self.model.author_id == filters["author_id"])

            # 视频特有的文件夹筛选
            if "folder_id" in filters and filters["folder_id"]:
                query = query.where(self.model.folder_id == filters["folder_id"])

            if status_filter == "draft":
                query = query.where(self.model.is_published == False)
            elif status_filter == "published":
                query = query.where(self.model.is_published == True, self.model.is_approved == True)
            elif status_filter in [
                VideoStatus.PUBLISHED_PENDING.value,
                VideoStatus.PUBLISHED_REJECTED.value,
            ]:
                # 使用 EXISTS 子查询来判断审核状态，比 JOIN 更高效
                review_status_filter = (
                    ReviewStatus.PENDING
                    if status_filter == VideoStatus.PUBLISHED_PENDING.value
                    else ReviewStatus.REJECTED
                )
                subquery = (
                    select(Review.id)
                    .where(
                        Review.content_id == self.model.id,
                        Review.content_type == ContentType.VIDEO,
                        Review.status == review_status_filter,
                    )
                    .exists()
                )
                query = query.where(self.model.is_published == True, subquery)

            # 权限检查：如果查询其他用户的视频，强制只能看发布的
            is_own_videos = current_user and current_user.id == filters.get("author_id")
            if filters.get("author_id") and not is_own_videos:
                query = query.where(self.model.is_published == True, self.model.is_approved == True)

            # 如果没有指定作者且不是特殊状态，默认只显示已发布且已审核的视频
            if not filters.get("author_id") and status_filter not in [
                "draft",
                "published",
                VideoStatus.PUBLISHED_PENDING.value,
                VideoStatus.PUBLISHED_REJECTED.value,
            ]:
                query = query.where(self.model.is_published == True, self.model.is_approved == True)

        # 使用游标分页器
        paginated_result = await CursorPaginator.paginate_query(
            db=db,
            query=query,
            params=params,
            cursor_field=params.order_by,
            include_total=include_total,
        )

        return paginated_result

    async def get_paginated_videos_by_user(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        params: CursorPaginationParams,
        status_filter: VideoStatus,
        is_own_videos: bool,
        include_review: bool,
        include_total: bool,
    ) -> CursorPaginationResponse:
        """
        获取指定用户的视频列表（统一接口-重构版）
        - 使用 selectinload 解决 N+1 问题
        - 使用 EXISTS 子查询代替 JOIN
        - 将复杂查询逻辑从 API endpoint 移至 CRUD 层
        """
        # 基础查询，并预加载所需关联数据
        query = select(self.model).options(
            selectinload(self.model.author),
            selectinload(self.model.tags),
            selectinload(self.model.category),
            selectinload(self.model.folder),
        )

        # 如果需要审核信息，也预加载
        if include_review and is_own_videos:
            query = query.options(
                selectinload(self.model.reviews).selectinload(models.Review.reviewer)
            )

        # 应用基础的用户筛选
        query = query.where(self.model.author_id == user_id)

        # 权限检查和状态筛选
        if not is_own_videos:
            # 查看其他用户的视频，只能看已发布且审核通过的
            query = query.where(self.model.is_published == True, self.model.is_approved == True)
        else:
            # 作者本人可以根据status参数筛选
            if status_filter == VideoStatus.DRAFT:
                query = query.where(self.model.is_published == False)
            elif status_filter == VideoStatus.PUBLISHED_APPROVED:
                query = query.where(self.model.is_published == True, self.model.is_approved == True)
            elif status_filter in [
                VideoStatus.PUBLISHED_PENDING,
                VideoStatus.PUBLISHED_REJECTED,
            ]:
                # 使用 EXISTS 子查询来判断审核状态，比 JOIN 更高效
                review_status_filter = (
                    ReviewStatus.PENDING
                    if status_filter == VideoStatus.PUBLISHED_PENDING
                    else ReviewStatus.REJECTED
                )
                subquery = (
                    select(Review.id)
                    .where(
                        Review.content_id == self.model.id,
                        Review.content_type == ContentType.VIDEO,
                        Review.status == review_status_filter,
                    )
                    .exists()
                )
                query = query.where(self.model.is_published == True, subquery)
            # "all" 状态不需要额外的where条件

        # 使用游标分页器
        paginated_result = await CursorPaginator.paginate_query(
            db=db,
            query=query,
            params=params,
            cursor_field=params.order_by,
            include_total=include_total,
        )

        return paginated_result


video = CRUDVideo(Video)
