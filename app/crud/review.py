from datetime import datetime
from typing import Any

from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.review import ContentType, Review, ReviewStatus
from app.schemas.review import ReviewCreate, ReviewUpdate


class CRUDReview(CRUDBase[Review, ReviewCreate, ReviewUpdate]):
    async def get_by_content(
        self, db: AsyncSession, *, content_type: ContentType, content_id: int
    ) -> Review | None:
        """根据内容类型和内容ID获取审核记录"""
        query = select(self.model).filter(
            self.model.content_type == content_type, self.model.content_id == content_id
        )
        result = await db.execute(query)
        return result.scalar_one_or_none()

    async def get_multi_with_filter(
        self,
        db: AsyncSession,
        *,
        skip: int = 0,
        limit: int = 100,
        content_type: ContentType | None = None,
        status: ReviewStatus | None = None,
    ) -> tuple[list[Review], int]:
        """获取审核列表，支持按内容类型和状态筛选"""
        query = select(self.model)

        if content_type:
            query = query.filter(self.model.content_type == content_type)

        if status:
            query = query.filter(self.model.status == status)

        count_query = select(func.count()).select_from(query.subquery())
        total = await db.scalar(count_query)

        query = query.order_by(self.model.created_at.desc()).offset(skip).limit(limit)
        result = await db.execute(query)
        items = result.scalars().all()

        return items, total

    def get_multi_with_filter_query(
        self,
        *,
        content_type: ContentType | None = None,
        status: ReviewStatus | None = None,
    ):
        """获取审核列表的查询对象，支持按内容类型和状态筛选"""
        query = select(self.model)

        if content_type:
            query = query.filter(self.model.content_type == content_type)

        if status:
            query = query.filter(self.model.status == status)

        return query

    async def update(
        self, db: AsyncSession, *, db_obj: Review, obj_in: ReviewUpdate | dict[str, Any]
    ) -> Review:
        """更新审核记录，如果状态变更为非待审核状态，则设置审核时间"""
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.dict(exclude_unset=True)

        # 如果状态从待审核变为其他状态，设置审核时间
        if (
            db_obj.status == ReviewStatus.PENDING
            and "status" in update_data
            and update_data["status"] != ReviewStatus.PENDING
        ):
            update_data["reviewed_at"] = datetime.utcnow()

        return await super().update(db, db_obj=db_obj, obj_in=update_data)


review = CRUDReview(Review)
