"""图片和视频处理相关的Celery任务"""

import os
from io import BytesIO

import ffmpeg
from PIL import Image

from app.core.celery import app
from app.core.config import get_settings
from app.services.logger import get_logger
from app.services.partial_upload import PartialUpload

logger = get_logger(__name__)
settings = get_settings()


@app.task(bind=True)
def process_and_upload_image(
    self, file_data: bytes, file_hash: str, quality: int = 80
) -> str | None:
    """将图片转换为webp格式并上传到OSS（GIF文件保持原格式）

    Args:
        file_data: 图片二进制数据
        file_hash: 文件哈希值
        quality: webp质量，默认80

    Returns:
        str: OSS文件URL，如果处理或上传失败返回None
    """
    try:
        # 检查图片格式
        with Image.open(BytesIO(file_data)) as img:
            # 如果是GIF格式，直接上传原文件
            if img.format == "GIF" or img.format == "gif":
                partial_client = PartialUpload()
                partial_client.upload(file_data, file_hash, file_type="gif")
                return f"/steam/images/{file_hash}.gif"

            # 其他格式转换为webp
            output_buffer = BytesIO()
            img.save(output_buffer, "webp", quality=quality)
            webp_data = output_buffer.getvalue()

        partial_client = PartialUpload()
        partial_client.upload(webp_data, file_hash)
        return f"/steam/images/{file_hash}.webp"
    except Exception as e:
        logger.error(f"Failed to process and upload image: {e}")
        return None


@app.task(bind=True)
def process_and_upload_video(
    self, file_data: bytes, file_hash: str, video_quality: str = "medium"
) -> dict | None:
    """将视频转换为webm格式并上传到OSS

    Args:
        file_data: 视频二进制数据
        file_hash: 文件哈希值
        video_quality: 视频质量，可选值：low, medium, high，默认medium

    Returns:
        dict: 包含文件URL和元数据的字典，如果处理或上传失败返回None
              格式: {"file_url": str, "duration": int, "width": int, "height": int}
    """
    try:
        # 保存原始视频到临时文件
        input_path = f"/tmp/{file_hash}_input.mp4"
        output_path = f"/tmp/{file_hash}_output.webm"

        with open(input_path, "wb") as f:
            f.write(file_data)

        try:
            # 首先获取视频元数据
            probe = ffmpeg.probe(input_path)
            video_stream = next(
                (stream for stream in probe["streams"] if stream["codec_type"] == "video"), None
            )

            if not video_stream:
                logger.error("No video stream found in the file")
                return None

            # 提取视频元数据
            duration = float(probe["format"]["duration"])
            width = int(video_stream["width"])
            height = int(video_stream["height"])

            logger.info(f"Video metadata - Duration: {duration}s, Resolution: {width}x{height}")

        except Exception as e:
            logger.error(f"Failed to extract video metadata: {e}")
            return None

        try:
            # 设置不同质量的编码参数
            quality_presets = {
                "low": {"crf": "35", "quality": "good", "cpu-used": "4"},
                "medium": {"crf": "30", "quality": "good", "cpu-used": "2"},
                "high": {"crf": "25", "quality": "best", "cpu-used": "0"},
            }
            preset = quality_presets.get(video_quality, quality_presets["medium"])

            # 使用ffmpeg处理视频，转换为WebM格式
            stream = ffmpeg.input(input_path)
            stream = ffmpeg.output(
                stream,
                output_path,
                vcodec="libvpx-vp9",  # 使用VP9编码
                acodec="libopus",  # 使用Opus音频编码
                **preset,
                threads="1",  # 启用行级多线程
                f="webm",  # 指定输出格式为webm
            )
            try:
                out, err = ffmpeg.run(
                    stream, overwrite_output=True, capture_stdout=True, capture_stderr=True
                )
            except ffmpeg.Error as e:
                logger.error(f"FFmpeg stderr:\n{e.stderr.decode()}")
                raise

            # 读取处理后的视频并上传
            with open(output_path, "rb") as f:
                processed_data = f.read()

            partial_client = PartialUpload()
            partial_client.upload(processed_data, file_hash, file_type="video")
            file_url = f"/steam/videos/{file_hash}.webm"

            # 返回包含元数据的字典
            return {
                "file_url": file_url,
                "duration": int(duration),  # 转换为整数秒
                "width": width,
                "height": height,
            }

        finally:
            # 清理临时文件
            if os.path.exists(input_path):
                os.remove(input_path)
            if os.path.exists(output_path):
                os.remove(output_path)

    except Exception as e:
        logger.error(f"Failed to process and upload video: {e}")
        return None
