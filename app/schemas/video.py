from datetime import datetime
from enum import Enum

from pydantic import BaseModel, field_serializer

from app.schemas.tag import TagBase


class VideoStatus(str, Enum):
    """视频状态枚举"""

    ALL = "all"  # 所有视频
    DRAFT = "draft"  # 草稿（未发布）
    PUBLISHED_APPROVED = "published_approved"  # 已发布且已审核通过
    PUBLISHED_PENDING = "published_pending"  # 已发布但待审核
    PUBLISHED_REJECTED = "published_rejected"  # 已发布但审核被拒绝
    LIKES = "likes"  # 用户点赞的视频
    FAVORITES = "favorites"  # 用户收藏的视频
    HISTORY = "history"  # 用户观看历史


class VideoBase(BaseModel):
    """视频基础模型"""

    title: str
    description: str | None = None
    url: str
    cover_url: str | None = None
    duration: int | None = None  # 视频时长（秒）
    width: int | None = None  # 视频宽度（像素）
    height: int | None = None  # 视频高度（像素）
    is_published: bool = False  # 是否发布（草稿状态为False）
    is_approved: bool = False  # 是否通过审核
    tags: list[TagBase] | None = None  # 标签列表


class VideoCreate(VideoBase):
    """创建视频的请求模型"""

    author_id: int
    folder_id: int | None = None  # 视频所属文件夹ID，如果不指定则使用用户的默认文件夹


class VideoUpdate(BaseModel):
    """更新视频的请求模型"""

    title: str | None = None
    description: str | None = None
    url: str | None = None
    cover_url: str | None = None
    duration: int | None = None
    width: int | None = None  # 视频宽度（像素）
    height: int | None = None  # 视频高度（像素）
    is_published: bool | None = None
    tags: list[str] | None = None
    category_id: int | None = None
    folder_id: int | None = None  # 添加文件夹ID，支持移动视频


class VideoInDBBase(VideoBase):
    """数据库中视频的基础模型"""

    id: int
    author_id: int
    category_id: int | None = None
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class Video(VideoInDBBase):
    """API响应中的视频模型"""

    @field_serializer("tags")
    def serialize_tags(self, tags: list[TagBase] | None) -> list[str] | None:
        """将标签对象列表序列化为字符串列表"""
        if tags is None:
            return None
        return [tag.name for tag in tags]


class VideoWithStats(VideoInDBBase):
    """包含统计信息的视频模型"""

    like_count: int = 0
    favorite_count: int = 0
    visit_count: int = 0  # 访问次数
    is_liked_by_user: bool = False
    is_favorited_by_user: bool = False

    @field_serializer("tags")
    def serialize_tags(self, tags: list[TagBase] | None) -> list[str] | None:
        """将标签对象列表序列化为字符串列表"""
        if tags is None:
            return None
        return [tag.name for tag in tags]


class VideoList(BaseModel):
    """视频列表响应模型"""

    total: int
    items: list[Video]


class VideoListWithStats(BaseModel):
    """包含统计信息的视频列表响应模型"""

    total: int
    items: list[VideoWithStats]


class VideoPlayListResponse(BaseModel):
    """视频播放列表响应模型，支持按文件夹分类显示视频"""

    total: int
    items: list[VideoWithStats]
    folder_info: dict | None = None  # 当list_type为folder_tree时，包含文件夹信息
