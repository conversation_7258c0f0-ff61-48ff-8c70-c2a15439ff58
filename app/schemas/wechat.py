"""微信相关的Pydantic模型"""

from typing import Any

from pydantic import BaseModel, Field

from app.schemas.unified_auth import UnifiedUserInfo


class QRCodeResponse(BaseModel):
    """二维码响应模型"""

    scene_str: str = Field(..., description="场景字符串")
    qr_url: str = Field(..., description="二维码图片URL")
    expire_seconds: int = Field(..., description="过期时间（秒）")


class LoginStatusResponse(BaseModel):
    """登录状态响应模型"""

    status: str = Field(..., description="登录状态")
    message: str | None = Field(None, description="状态消息")
    user: UnifiedUserInfo | None = Field(None, description="统一用户信息")
    access_token: str | None = Field(None, description="访问令牌")
    token_type: str | None = Field(None, description="令牌类型")
    openId: str | None = Field(None, description="微信OpenID")


class WeChatUserInfo(BaseModel):
    """微信用户信息模型"""

    openid: str
    nickname: str
    sex: int
    province: str
    city: str
    country: str
    headimgurl: str
    privilege: list = []
    unionid: str | None = None


class WeChatLoginRequest(BaseModel):
    """微信登录请求模型"""

    scene_str: str


class WeChatBindRequest(BaseModel):
    """微信绑定请求模型"""

    openid: str = Field(..., description="微信OpenID", min_length=1)
    username: str = Field(..., description="用户名（手机号）", min_length=11, max_length=11)
    code: str = Field(..., description="短信验证码", min_length=4, max_length=6)

    class Config:
        json_schema_extra = {
            "example": {"openid": "wx_openid_123456", "username": "13800138000", "code": "123456"}
        }


class WeChatBindResponse(BaseModel):
    """微信绑定响应模型"""

    message: str = Field(..., description="操作结果消息")
    user: UnifiedUserInfo = Field(..., description="用户信息")
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(..., description="令牌类型")


class WeChatInfoResponse(BaseModel):
    """微信信息响应模型"""

    is_bound: bool = Field(..., description="是否已绑定微信")
    wechat_nickname: str | None = Field(None, description="微信昵称")
    wechat_avatar: str | None = Field(None, description="微信头像")
    login_type: str = Field(..., description="登录方式")


class WeChatUnbindResponse(BaseModel):
    """微信解绑响应模型"""

    message: str = Field(..., description="操作结果消息")
    user: dict[str, Any] = Field(..., description="用户信息")


class WeChatMessageEvent(BaseModel):
    """微信消息事件模型"""

    ToUserName: str
    FromUserName: str
    CreateTime: int
    MsgType: str
    Event: str | None = None
    EventKey: str | None = None
    Ticket: str | None = None
