#!/usr/bin/env python3
"""
检查数据库 schema 是否正确添加了新字段
"""

import asyncio
from sqlalchemy import text
from app.db.session import SessionLocal


async def check_video_table_schema():
    """检查 videos 表的 schema"""
    
    async with SessionLocal() as db:
        # 查询 videos 表的列信息
        result = await db.execute(text("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'videos' 
            AND table_schema = 'public'
            ORDER BY ordinal_position;
        """))
        
        columns = result.fetchall()
        
        print("📋 videos 表的字段信息:")
        print("-" * 60)
        
        width_found = False
        height_found = False
        duration_found = False
        
        for column in columns:
            column_name, data_type, is_nullable, column_default = column
            print(f"{column_name:20} | {data_type:15} | {is_nullable:8} | {column_default or 'NULL'}")
            
            if column_name == 'width':
                width_found = True
            elif column_name == 'height':
                height_found = True
            elif column_name == 'duration':
                duration_found = True
        
        print("-" * 60)
        
        # 验证新字段
        print("\n🔍 字段验证结果:")
        print(f"✅ duration 字段: {'存在' if duration_found else '❌ 不存在'}")
        print(f"✅ width 字段: {'存在' if width_found else '❌ 不存在'}")
        print(f"✅ height 字段: {'存在' if height_found else '❌ 不存在'}")
        
        if width_found and height_found and duration_found:
            print("\n🎉 所有新字段都已成功添加到数据库!")
        else:
            print("\n❌ 部分字段缺失，请检查迁移是否正确执行")


async def main():
    """主函数"""
    print("🚀 检查数据库 schema...\n")
    
    try:
        await check_video_table_schema()
        
    except Exception as e:
        print(f"\n❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
