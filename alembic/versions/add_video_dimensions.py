"""添加视频宽度和高度字段

Revision ID: add_video_dimensions
Revises: f76e5a04879e
Create Date: 2025-01-17 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_video_dimensions'
down_revision = 'f76e5a04879e'
branch_labels = None
depends_on = None


def upgrade():
    """添加视频宽度和高度字段"""
    # 添加 width 字段
    op.add_column('videos', sa.Column('width', sa.Integer(), nullable=True, comment='视频宽度（像素）'))
    
    # 添加 height 字段
    op.add_column('videos', sa.Column('height', sa.Integer(), nullable=True, comment='视频高度（像素）'))


def downgrade():
    """移除视频宽度和高度字段"""
    # 移除 height 字段
    op.drop_column('videos', 'height')
    
    # 移除 width 字段
    op.drop_column('videos', 'width')
