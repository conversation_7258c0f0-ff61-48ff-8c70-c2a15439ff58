# 视频尺寸字段 API 使用示例

## 📋 新增字段说明

在视频更新 API 中新增了三个技术参数字段：

- `duration`: 视频时长（秒）
- `width`: 视频宽度（像素）
- `height`: 视频高度（像素）

## 🔧 API 端点

```http
PUT /api/v1/videos/{video_id}
```

## 📝 使用示例

### 1. 创建包含尺寸信息的视频

```json
POST /api/v1/videos/
{
  "title": "4K 演示视频",
  "description": "高清演示视频",
  "url": "https://example.com/demo-4k.mp4",
  "cover_url": "https://example.com/demo-cover.jpg",
  "duration": 300,
  "width": 3840,
  "height": 2160
}
```

### 2. 更新视频的技术参数

```json
PUT /api/v1/videos/123
{
  "duration": 450,
  "width": 1920,
  "height": 1080
}
```

### 3. 同时更新内容和技术参数

```json
PUT /api/v1/videos/123
{
  "title": "更新后的标题",
  "description": "更新后的描述",
  "duration": 600,
  "width": 2560,
  "height": 1440
}
```

### 4. 部分更新（只更新某些字段）

```json
PUT /api/v1/videos/123
{
  "width": 1920
}
```

### 5. 清除技术参数（设置为 null）

```json
PUT /api/v1/videos/123
{
  "duration": null,
  "width": null,
  "height": null
}
```

## 🎯 常见使用场景

### 视频上传后的元数据更新

```javascript
// 1. 用户上传视频文件
const uploadResponse = await uploadVideo(videoFile);

// 2. 获取视频元数据（通过视频处理服务）
const metadata = await getVideoMetadata(uploadResponse.url);

// 3. 更新视频的技术参数
const updateResponse = await fetch(`/api/v1/videos/${videoId}`, {
  method: 'PUT',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    duration: metadata.duration,
    width: metadata.width,
    height: metadata.height
  })
});
```

### 视频质量标识

```javascript
// 根据视频尺寸显示质量标签
function getQualityLabel(width, height) {
  if (width >= 3840 && height >= 2160) return '4K';
  if (width >= 2560 && height >= 1440) return '2K';
  if (width >= 1920 && height >= 1080) return 'HD';
  if (width >= 1280 && height >= 720) return 'HD Ready';
  return 'SD';
}

// 在视频列表中显示
videos.forEach(video => {
  const quality = getQualityLabel(video.width, video.height);
  console.log(`${video.title} - ${quality} (${video.width}x${video.height})`);
});
```

### 响应式视频播放器

```javascript
// 根据视频尺寸调整播放器
function setupVideoPlayer(video) {
  const player = document.getElementById('video-player');
  
  if (video.width && video.height) {
    const aspectRatio = video.width / video.height;
    
    // 设置播放器宽高比
    player.style.aspectRatio = aspectRatio;
    
    // 根据屏幕大小调整播放器尺寸
    const maxWidth = Math.min(window.innerWidth * 0.9, video.width);
    const maxHeight = maxWidth / aspectRatio;
    
    player.style.maxWidth = `${maxWidth}px`;
    player.style.maxHeight = `${maxHeight}px`;
  }
}
```

## 🔍 响应示例

### 成功响应

```json
{
  "id": 123,
  "title": "演示视频",
  "description": "这是一个演示视频",
  "url": "https://example.com/demo.mp4",
  "cover_url": "https://example.com/cover.jpg",
  "duration": 300,
  "width": 1920,
  "height": 1080,
  "is_published": true,
  "is_approved": true,
  "author_id": 1,
  "folder_id": 5,
  "created_at": "2025-01-17T12:00:00Z",
  "updated_at": "2025-01-17T12:30:00Z"
}
```

## ⚠️ 注意事项

1. **数据类型**: 所有尺寸字段都是整数类型（像素值）
2. **可选字段**: 这些字段都是可选的，可以为 `null`
3. **内容更新**: 更新这些技术参数会触发内容更新检查
4. **权限控制**: 只有视频作者或有相应权限的用户才能更新
5. **审核流程**: 如果视频已发布，更新技术参数可能需要重新审核

## 🚀 最佳实践

1. **上传时设置**: 在视频上传完成后立即设置这些参数
2. **批量更新**: 对于大量视频，考虑使用批量更新接口
3. **缓存策略**: 这些参数变化不频繁，适合缓存
4. **验证数据**: 客户端应验证尺寸数据的合理性
5. **错误处理**: 妥善处理参数为空的情况
