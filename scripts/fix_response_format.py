#!/usr/bin/env python3
"""
响应格式修复脚本

检查和修复项目中的响应格式问题
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.utils.response_validator import ResponseValidator, ResponseFormatChecker
from app.services.logger import get_logger

logger = get_logger(__name__)


class ResponseFormatFixer:
    """响应格式修复器"""
    
    def __init__(self):
        self.checker = ResponseFormatChecker(enabled=True)
        self.issues_found = []
        self.fixes_applied = []
    
    def check_middleware_configuration(self):
        """检查中间件配置"""
        logger.info("检查中间件配置...")
        
        try:
            from app.main import app
            
            # 检查中间件顺序
            middleware_stack = []
            for middleware in app.user_middleware:
                middleware_stack.append(middleware.cls.__name__)
            
            logger.info(f"中间件栈: {middleware_stack}")
            
            # 检查是否有ResponseFormatterMiddleware
            if "ResponseFormatterMiddleware" not in middleware_stack:
                self.issues_found.append("缺少ResponseFormatterMiddleware")
            
            # 检查中间件顺序
            expected_order = [
                "CORSMiddleware",
                "ResponseFormatterMiddleware", 
                "VisitStatsMiddleware",
                "ExceptionHandlerMiddleware"
            ]
            
            for i, expected in enumerate(expected_order):
                if i < len(middleware_stack) and middleware_stack[i] != expected:
                    self.issues_found.append(f"中间件顺序问题: 期望 {expected}, 实际 {middleware_stack[i]}")
            
        except Exception as e:
            self.issues_found.append(f"中间件配置检查失败: {e}")
    
    def check_exception_handlers(self):
        """检查异常处理器"""
        logger.info("检查异常处理器...")
        
        try:
            from app.main import app
            
            # 检查是否有必要的异常处理器
            exception_handlers = list(app.exception_handlers.keys())
            logger.info(f"异常处理器: {exception_handlers}")
            
            # 检查是否有HTTPException和RequestValidationError处理器
            from fastapi import HTTPException
            from fastapi.exceptions import RequestValidationError
            
            if HTTPException not in exception_handlers:
                self.issues_found.append("缺少HTTPException处理器")
            
            if RequestValidationError not in exception_handlers:
                self.issues_found.append("缺少RequestValidationError处理器")
            
        except Exception as e:
            self.issues_found.append(f"异常处理器检查失败: {e}")
    
    def check_response_models(self):
        """检查响应模型"""
        logger.info("检查响应模型...")
        
        try:
            # 检查UnifiedContentResponse模型
            from app.schemas.simple_unified_response import UnifiedContentResponse
            
            # 创建一个测试实例
            test_data = {
                "id": 1,
                "title": "测试",
                "description": "测试描述",
                "is_published": True,
                "is_approved": True,
                "created_at": "2023-01-01T00:00:00",
                "updated_at": "2023-01-01T00:00:00",
                "author": {
                    "id": 1,
                    "username": "test",
                    "nickname": "测试用户",
                    "avatar": None
                }
            }
            
            response = UnifiedContentResponse(**test_data)
            logger.info("UnifiedContentResponse模型验证通过")
            
        except Exception as e:
            self.issues_found.append(f"响应模型检查失败: {e}")
    
    def check_api_endpoints(self):
        """检查API端点"""
        logger.info("检查API端点...")
        
        try:
            from fastapi.testclient import TestClient
            from app.main import app
            
            client = TestClient(app)
            
            # 测试一些关键端点
            test_endpoints = [
                ("/health", 200),
                ("/api/v1/categories/tree", 200),
                ("/api/v1/nonexistent", 404),
            ]
            
            for endpoint, expected_status in test_endpoints:
                try:
                    response = client.get(endpoint)
                    
                    if response.status_code != expected_status:
                        self.issues_found.append(
                            f"端点 {endpoint} 状态码错误: 期望 {expected_status}, 实际 {response.status_code}"
                        )
                        continue
                    
                    # 检查响应格式
                    data = response.json()
                    
                    if endpoint.startswith("/api/v1"):
                        # API端点应该有统一格式
                        if response.status_code < 400:
                            if not ResponseValidator.validate_success_response(data):
                                self.issues_found.append(f"端点 {endpoint} 成功响应格式无效")
                        else:
                            if not ResponseValidator.validate_error_response(data):
                                self.issues_found.append(f"端点 {endpoint} 错误响应格式无效")
                    
                    logger.info(f"端点 {endpoint} 检查通过")
                    
                except Exception as e:
                    self.issues_found.append(f"端点 {endpoint} 检查失败: {e}")
            
        except Exception as e:
            self.issues_found.append(f"API端点检查失败: {e}")
    
    def generate_fix_recommendations(self):
        """生成修复建议"""
        logger.info("生成修复建议...")
        
        recommendations = []
        
        for issue in self.issues_found:
            if "缺少ResponseFormatterMiddleware" in issue:
                recommendations.append(
                    "在 app/main.py 中添加 ResponseFormatterMiddleware 到中间件栈"
                )
            elif "中间件顺序问题" in issue:
                recommendations.append(
                    "调整 app/main.py 中的中间件顺序，确保正确的执行顺序"
                )
            elif "缺少HTTPException处理器" in issue:
                recommendations.append(
                    "在 app/main.py 中添加 HTTPException 异常处理器"
                )
            elif "响应格式无效" in issue:
                recommendations.append(
                    "检查相关API端点的响应构建逻辑，确保使用统一的响应格式"
                )
            elif "双重嵌套" in issue:
                recommendations.append(
                    "检查响应构建代码，避免手动添加status字段导致的双重包装"
                )
        
        return recommendations
    
    async def run_full_check(self):
        """运行完整检查"""
        logger.info("开始响应格式检查...")
        
        self.check_middleware_configuration()
        self.check_exception_handlers()
        self.check_response_models()
        self.check_api_endpoints()
        
        logger.info(f"检查完成，发现 {len(self.issues_found)} 个问题")
        
        if self.issues_found:
            logger.warning("发现的问题:")
            for i, issue in enumerate(self.issues_found, 1):
                logger.warning(f"  {i}. {issue}")
            
            recommendations = self.generate_fix_recommendations()
            if recommendations:
                logger.info("修复建议:")
                for i, rec in enumerate(recommendations, 1):
                    logger.info(f"  {i}. {rec}")
        else:
            logger.info("✅ 未发现响应格式问题")
        
        return len(self.issues_found) == 0


async def main():
    """主函数"""
    fixer = ResponseFormatFixer()
    
    try:
        success = await fixer.run_full_check()
        
        if success:
            logger.info("🎉 响应格式检查通过！")
            return 0
        else:
            logger.error("❌ 发现响应格式问题，请根据建议进行修复")
            return 1
            
    except Exception as e:
        logger.error(f"检查过程中发生异常: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
